# frozen_string_literal: true

require 'test_helper'

class ActionTest < ActiveSupport::TestCase
  def setup
    @practice = practices(:one)
    @patient = patients(:one)
    @user = users(:one)
    @calendar_booking = calendar_bookings(:one)
  end

  test "assigned_to_appointment? returns true when appointment_id is present" do
    action = Action.new(appointment_id: @calendar_booking.id)
    assert action.assigned_to_appointment?
  end

  test "assigned_to_appointment? returns false when appointment_id is nil" do
    action = Action.new(appointment_id: nil)
    assert_not action.assigned_to_appointment?
  end

  test "appointment_booked? returns true when appointment has patient" do
    @calendar_booking.update!(patient: @patient)
    action = Action.new(appointment: @calendar_booking)
    assert action.appointment_booked?
  end

  test "appointment_booked? returns false when appointment has no patient" do
    @calendar_booking.update!(patient: nil)
    action = Action.new(appointment: @calendar_booking)
    assert_not action.appointment_booked?
  end

  test "appointment_status returns 'Unbooked' when appointment has no patient" do
    @calendar_booking.update!(patient: nil)
    action = Action.new(appointment: @calendar_booking)
    assert_equal 'Unbooked', action.appointment_status
  end

  test "appointment_status returns humanized status when appointment is booked" do
    @calendar_booking.update!(patient: @patient, status: :scheduled)
    action = Action.new(appointment: @calendar_booking)
    assert_equal 'Scheduled', action.appointment_status
  end

  test "effective_due_date returns appointment start_time when assigned to appointment" do
    start_time = 1.day.from_now
    @calendar_booking.update!(start_time: start_time)
    action = Action.new(appointment: @calendar_booking, date_due: 2.days.from_now)
    assert_equal start_time, action.effective_due_date
  end

  test "effective_due_date returns date_due when not assigned to appointment" do
    due_date = 2.days.from_now
    action = Action.new(date_due: due_date)
    assert_equal due_date, action.effective_due_date
  end

  test "due_date_display shows appointment unbooked message when appointment has no patient" do
    @calendar_booking.update!(patient: nil)
    action = Action.new(appointment: @calendar_booking)
    assert_equal "Appointment unbooked - Due when scheduled", action.due_date_display
  end

  test "due_date_display shows appointment date and time when booked" do
    start_time = Time.zone.parse("2024-01-15 14:30")
    @calendar_booking.update!(patient: @patient, start_time: start_time)
    action = Action.new(appointment: @calendar_booking)
    expected = "Due: 15/01/2024 at 14:30"
    assert_equal expected, action.due_date_display
  end

  test "due_date_display shows regular due date when not assigned to appointment" do
    due_date = Time.zone.parse("2024-01-15 14:30")
    action = Action.new(date_due: due_date)
    expected = "Due: 15/01/2024"
    assert_equal expected, action.due_date_display
  end

  test "overdue? returns true when appointment is in the past" do
    past_time = 1.day.ago
    @calendar_booking.update!(start_time: past_time)
    action = Action.new(appointment: @calendar_booking)
    assert action.overdue?
  end

  test "overdue? returns false when appointment is in the future" do
    future_time = 1.day.from_now
    @calendar_booking.update!(start_time: future_time)
    action = Action.new(appointment: @calendar_booking)
    assert_not action.overdue?
  end

  test "days_until_due returns negative number when overdue" do
    past_time = 2.days.ago
    @calendar_booking.update!(start_time: past_time)
    action = Action.new(appointment: @calendar_booking)
    assert_equal(-2, action.days_until_due)
  end

  test "days_until_due returns positive number when due in future" do
    future_time = 3.days.from_now
    @calendar_booking.update!(start_time: future_time)
    action = Action.new(appointment: @calendar_booking)
    assert_equal 3, action.days_until_due
  end

  test "due_today_or_no_date scope includes appointment-assigned actions due today" do
    today_appointment = calendar_bookings(:today)
    today_appointment.update!(start_time: Time.current.beginning_of_day + 10.hours)
    
    action = Action.create!(
      actionable: @patient,
      appointment: today_appointment,
      action_type: 'task',
      title: 'Test action'
    )

    assert_includes Action.due_today_or_no_date, action
  end

  test "due_today_or_no_date scope excludes appointment-assigned actions not due today" do
    future_appointment = calendar_bookings(:future)
    future_appointment.update!(start_time: 2.days.from_now)
    
    action = Action.create!(
      actionable: @patient,
      appointment: future_appointment,
      action_type: 'task',
      title: 'Test action'
    )

    assert_not_includes Action.due_today_or_no_date, action
  end
end
