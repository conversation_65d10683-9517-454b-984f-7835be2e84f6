# frozen_string_literal: true

require 'zip' # For zipping assets

module Admin
  class PatientsController < Admin::ApplicationController
    include Admin::ConversationsHelper
    include Admin::PatientAssetsHelper
    include Admin::ActionsHelper

    before_action :set_patient, only: %i[
      show update conversation linked_conversation actions assets
      update_asset_labels archive_assets forgot_password reset_password
      prescriptions archive toggle_archive update_teethgrid_view update_field
      update_assigned_staff update_assigned_practices add_payment_plan
      remove_payment_plan remove_team_member login_as_patient available_devices
      download_assets notes treatment_plans_data
    ]
    before_action :set_users_and_practices, only: %i[new show create update]
    before_action :set_alerts, only: %i[new show create update]
    before_action :set_gps, only: %i[new show create update]
    before_action :set_payment_plans, only: %i[new show create update]
    before_action :set_notes, only: %i[show update]

    include Admin::ConversationsHelper
    include Admin::PatientAssetsHelper
    include Admin::ActionsHelper

    def index
      index_params = params.permit(:q, :archived, :page, :format)

      # Determine archived status - handle both string and boolean values
      archived_status = case index_params[:archived]
                        when 'true', true
                          true
                        when 'false', false
                          false
                        else
                          false # Default to showing active patients
                        end

      @patients = policy_scope(Patient)
                  .includes(practices_patients: :practice)
                  .permanent
                  .where(archived: index_params[:archived] == 'true')

      # Apply search if query parameter is present
      @patients = @patients.ransack(fuzzy_search: index_params[:q]).result(distinct: true) if index_params[:q].present?

      @patients = @patients.order('patients.first_name ASC, patients.last_name ASC')
                           .paginate(page: index_params[:page], per_page: 20)

      respond_to do |format|
        format.html
        format.json do
          table_html = render_to_string(
            partial: 'admin/patients/patients_table',
            locals: { patients: @patients, archived: archived_status },
            formats: [:html]
          )

          pagination_html = if @patients.empty?
                              ''
                            else
                              render_to_string(
                                partial: 'admin/patients/pagination',
                                locals: { patients: @patients },
                                formats: [:html]
                              )
                            end

          render json: {
            success: true,
            table_html: table_html,
            pagination_html: pagination_html,
            total_count: @patients.total_entries
          }
        end
      end
    end

    def new
      @patient = Patient.new
    end

    def show
      @linked_family_members = []

      # Track recently viewed patients for the current user
      track_recently_viewed_patient(@patient)

      respond_to do |format|
        format.html

        # NOTE: used in Secure Send
        format.json do
          render json: { patient: @patient }
        end
      end
    end

    def linked_conversation
      conversation = @patient.linked_conversation
      render json: { conversation_id: conversation.id }
    rescue StandardError => e
      render json: { error: e.message }, status: :unprocessable_entity
    end

    def conversation
      # Eager load conversation and author associations to prevent N+1 queries
      @conversation = @patient.linked_conversation

      # Only load messages if conversation exists
      if @conversation
        @messages = @conversation.conversation_messages.includes(:author)
        @messages = @messages.where(label: params[:message_filter]).order(created_at: :asc) if params[:message_filter].present?
        if params[:filter].present?
          @messages = @messages.where(message_type: 'outbound',
                                      from: params[:filter]).or(@messages.where.not(message_type: 'outbound'))
        end

        @messages = @messages.order(created_at: :asc)
      else
        @messages = []
      end

      @whatsapp_templates = load_whatsapp_templates
      @letter_templates = load_letter_templates
    end

    def actions
      # Eager load created_by and actionable associations to prevent N+1 queries
      # Order actions: overdue first, then future due dates, then no due date
      @actions = @patient.actions.includes(:created_by, :actionable).with_deleted
                         .order(
                           Arel.sql(
                             "CASE
                               WHEN date_due IS NULL THEN 3
                               WHEN date_due < NOW() THEN 1
                               ELSE 2
                             END,
                             date_due ASC NULLS LAST,
                             created_at DESC"
                           )
                         )
      @comments = fetch_comments(@actions)
    end

    def treatment_plans_data
      # Return treatment plans data for AJAX requests
      treatment_plans = @patient.course_of_treatments
                                .includes(charting_appointments: :dentist)
                                .where(archived: false)
                                .order(created_at: :desc)

      plans_data = treatment_plans.map do |plan|
        appointments_data = plan.charting_appointments.order(:position).map do |appt|
          # Use actual calendar booking date if available, otherwise show position
          appointment_date = if appt.calendar_booking&.start_time.present?
                               appt.calendar_booking.start_time.strftime('%a, %d %b %Y at %I:%M %p')
                             else
                               "Appointment #{appt.position}"
                             end
          [
            appt.id,
            appointment_date,
            appt.dentist_id,
            appt.dentist&.full_name || 'Unknown'
          ]
        end

        {
          id: plan.id,
          name: plan.name || "Treatment Plan #{plan.id}",
          appointments: appointments_data
        }
      end

      render json: {
        success: true,
        treatment_plans: plans_data
      }
    rescue StandardError => e
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end

    def assets
      # Load all assets for JavaScript filtering
      @assets = @patient.patient_assets.order('created_at DESC')

      # For backward compatibility, still support server-side filtering if needed
      show_archived = params[:archived] == 'true'
      @assets = @assets.where(archived: show_archived) if params[:archived].present?

      # Filter by label if selected (for server-side filtering)
      @assets = @assets.where(label: params[:label]) if params[:label].present? && params[:label] != 'all'

      @labels = [*DEFAULT_LABELS, *PatientAssetLabel.pluck(:label)]
      @selected_label = params[:label].presence || 'all'
      @show_archived = show_archived

      respond_to do |format|
        format.html
        format.json do
          # Format assets for JSON response
          assets_json = @assets.map do |asset|
            {
              id: asset.id,
              label: asset.label,
              filename: asset.file.attached? ? asset.file.filename.to_s : 'No file attached',
              created_at: asset.created_at.strftime('%-d/%-m/%Y %H:%M'),
              badge_class: asset_badge_class(asset.label),
              file_url: asset.file.attached? ? url_for(asset.file) : nil,
              archived: asset.archived
            }
          end

          render json: {
            success: true,
            assets: assets_json,
            count: assets_json.length,
            show_archived: show_archived,
            selected_label: @selected_label
          }
        end
      end
    end

    def update_asset_labels
      asset_ids = params[:asset_ids]
      label = params[:label]

      if asset_ids.blank? || label.blank?
        render json: { success: false, message: 'Missing asset IDs or label' }, status: :unprocessable_entity
        return
      end

      begin
        updated_assets = PatientAsset.where(id: asset_ids)
        updated_assets.update_all(label: label)

        # Return updated assets with their new label for AJAX updates
        assets_data = updated_assets.reload.map do |asset|
          {
            id: asset.id,
            label: asset.label,
            badge_class: asset_badge_class(asset.label)
          }
        end

        render json: {
          success: true,
          message: 'Asset labels updated successfully',
          assets: assets_data
        }
      rescue StandardError => e
        render json: { success: false, message: "Error updating asset labels: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def archive_assets
      asset_ids = params[:asset_ids]

      if asset_ids.blank?
        render json: { success: false, message: 'No assets selected' }, status: :unprocessable_entity
        return
      end

      begin
        # Convert the archived parameter to a boolean
        archived = params[:archived].to_s.downcase == 'true' || params[:archived] == true

        # If we're viewing archived assets and the action is to restore (archived=false)
        # or we're viewing non-archived assets and the action is to archive (archived=true)
        PatientAsset.where(id: asset_ids).update_all(archived: archived)

        message = archived ? 'Assets archived successfully' : 'Assets restored successfully'

        render json: {
          success: true,
          message: message,
          asset_ids: asset_ids
        }
      rescue StandardError => e
        render json: { success: false, message: "Error archiving assets: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def prescriptions
      @prescriptions = @patient.prescriptions
    end

    def create_temporary
      # Determine practice assignment based on user context
      practice_to_assign = determine_practice_for_new_patient

      # If practice_to_assign is nil, it means we need to show practice selection modal
      if practice_to_assign.nil?
        respond_to do |format|
          format.html { redirect_to admin_patients_path, alert: 'Please select a practice first.' }
          format.json { render json: { success: false, show_practice_selector: true } }
        end
        return
      end

      create_temporary_patient_with_practice(practice_to_assign)
    end

    def create_temporary_with_practice
      practice_id = params[:practice_id]

      if practice_id.blank?
        respond_to do |format|
          format.json { render json: { success: false, error: 'Practice ID is required' } }
        end
        return
      end

      practice = current_user.practices.find_by(id: practice_id)

      unless practice
        respond_to do |format|
          format.json { render json: { success: false, error: 'Invalid practice selected' } }
        end
        return
      end

      create_temporary_patient_with_practice(practice)
    end

    def create_with_practice
      practice_id = params[:practice_id]

      if practice_id.blank?
        respond_to do |format|
          format.json { render json: { success: false, error: 'Practice ID is required' } }
        end
        return
      end

      practice = current_user.practices.find_by(id: practice_id)

      unless practice
        respond_to do |format|
          format.json { render json: { success: false, error: 'Invalid practice selected' } }
        end
        return
      end

      create_patient_with_practice(practice)
    end

    def create
      @patient = Patient.new(patient_params)
      @patient.created_by_id = current_user.id

      # If this was a temporary patient being saved properly, remove temporary flag
      @patient.temporary = false if @patient.temporary?

      if params[:saved]
        # Determine practice assignment based on user context
        practice_to_assign = determine_practice_for_new_patient

        # If practice_to_assign is nil, it means we need to show practice selection modal
        if practice_to_assign.nil?
          respond_to do |format|
            format.html { redirect_to admin_patients_path, alert: 'Please select a practice first.' }
            format.json { render json: { success: false, show_practice_selector: true } }
          end
          return
        end

        if @patient.save
          # Set the current practice
          @patient.current_practice = practice_to_assign

          # Assign the practice to the patient
          @patient.practices << practice_to_assign

          # Assign the current user as a team member
          @patient.assigned_staff << current_user

          # Assign default COT payment plan
          @patient.assign_default_cot_payment_plan
          @patient.save if @patient.changed?

          flash[:success] = 'Patient created'
          redirect_to admin_patients_path
        else
          flash[:error] = ['Error creating patient', @patient.errors.first&.full_message]
          render :new
        end
      else
        render :new
      end
    end

    def update
      # Handle JSON requests properly
      if request.content_type =~ /json/
        # Parse JSON request body for JSON requests
        json_params = JSON.parse(request.body.read)
        params.merge!(json_params) if json_params.is_a?(Hash)
      end

      # Check if this is an image-only update
      permitted_params = patient_params
      is_image_only_update = permitted_params.keys == ['image'] && permitted_params['image'].present?

      if is_image_only_update
        # Handle image-only updates without triggering full validations
        begin
          ActiveRecord::Base.transaction do
            # Remove existing image attachment if present
            @patient.image.attachment.delete if @patient.image.attached?

            # Attach new image directly using Active Storage without model validations
            uploaded_file = permitted_params['image']
            blob = ActiveStorage::Blob.create_and_upload!(
              io: uploaded_file.tempfile,
              filename: uploaded_file.original_filename,
              content_type: uploaded_file.content_type
            )

            # Create the attachment directly without triggering model callbacks
            attachment = ActiveStorage::Attachment.new(
              name: 'image',
              record: @patient,
              blob: blob
            )
            attachment.save!(validate: false)

            # Clear temporary flag when saving with valid data
            @patient.update_column(:temporary, false) if @patient.temporary?
          end

          respond_to do |format|
            format.html do
              flash[:success] = 'Patient image updated'
              redirect_to admin_patient_path(@patient)
            end
            format.json do
              render json: { success: true, patient: @patient }
            end
            format.any do
              render json: { success: true, patient: @patient }
            end
          end
        rescue StandardError => e
          respond_to do |format|
            format.html do
              flash[:error] = "Error updating patient image: #{e.message}"
              render :show
            end
            format.json do
              render json: { success: false, errors: ["Failed to update image: #{e.message}"] }, status: :unprocessable_entity
            end
            format.any do
              render json: { success: false, errors: ["Failed to update image: #{e.message}"] }, status: :unprocessable_entity
            end
          end
        end
      else
        # Handle regular updates with full validations
        @patient.assign_attributes(permitted_params)
        # Clear temporary flag when saving with valid data
        @patient.temporary = false if @patient.temporary?

        respond_to do |format|
          if @patient.save
            format.html do
              flash[:success] = 'Patient updated'
              redirect_to admin_patient_path(@patient)
            end
            format.json do
              render json: { success: true, patient: @patient }
            end
            format.any do
              render json: { success: true, patient: @patient }
            end
          else
            format.html do
              flash[:error] = ['Error updating patient', @patient.errors.first&.full_message]
              render :show
            end
            format.json do
              render json: { success: false, errors: @patient.errors.full_messages }, status: :unprocessable_entity
            end
            format.any do
              render json: { success: false, errors: @patient.errors.full_messages }, status: :unprocessable_entity
            end
          end
        end
      end
    rescue JSON::ParserError => e
      # Handle JSON parsing errors
      respond_to do |format|
        format.json { render json: { success: false, error: "Invalid JSON: #{e.message}" }, status: :bad_request }
        format.any { render json: { success: false, error: "Invalid JSON: #{e.message}" }, status: :bad_request }
      end
    end

    def forgot_password
      @patient.send_reset_password_instructions
      flash[:success] = 'Password reset email sent'
      redirect_to admin_patient_path(@patient)
    end

    def reset_password
      random_password = SecureRandom.hex(4)
      @patient.update(password: random_password, force_password_reset: true)

      render json: { password: random_password }
    end

    def search
      query = params[:q] || params[:query]

      # Handle recently viewed patients from localStorage (sent via recently_viewed_ids param)
      recently_viewed_patients = []
      if params[:recently_viewed_ids].present?
        recently_viewed_ids = params[:recently_viewed_ids].split(',').map(&:to_i)
        recently_viewed_patients = policy_scope(Patient)
                                   .permanent
                                   .where(id: recently_viewed_ids)
                                   .includes(:practices_patients)
                                   .limit(10)
        # Maintain the order from localStorage
        recently_viewed_patients = recently_viewed_ids.map { |id| recently_viewed_patients.find { |p| p.id == id } }.compact
      end

      # Perform search if query is provided
      patients = []
      patients = policy_scope(Patient).permanent.ransack(fuzzy_search: query).result(distinct: true).limit(10) if query.present?

      # Fallback to session-based last visited patients if no localStorage data
      last_visited_patients = []
      if recently_viewed_patients.empty? && session[:last_visited_patients].present?
        last_visited_patients = policy_scope(Patient)
                                .permanent
                                .where(id: session[:last_visited_patients])
                                .limit(10)
      end

      respond_to do |format|
        format.html do
          render partial: 'admin/patients/search', locals: {
            patients: patients,
            recently_viewed_patients: recently_viewed_patients,
            last_visited_patients: last_visited_patients,
            show_recently_viewed: query.blank? && (recently_viewed_patients.any? || last_visited_patients.any?)
          }
        end
        format.json do
          # Return JSON data for patient selection
          patient_data = patients.map do |patient|
            {
              id: patient.id,
              name: patient.full_name,
              dob: patient.date_of_birth&.strftime('%d %b %Y'),
              address: patient.postcode,
              image_url: patient.image.attached? ? url_for(patient.image) : nil
            }
          end
          render json: patient_data
        end
      end
    end

    def select2_search
      # Start with base patient scope, excluding temporary patients
      patients_scope = policy_scope(Patient).where(temporary: false)

      # Filter by practice ID - check both direct practice_id param and board_id param
      practice_id = nil

      if params[:practice_id].present?
        # Direct practice ID provided (from CRM patient search)
        practice_id = params[:practice_id]

      elsif params[:board_id].present?
        # Board ID provided, need to find practice through board
        board = CrmBoard.find_by(id: params[:board_id])

        practice_id = board.practice_id if board && board.practice_id.present?
      end

      # Apply practice filtering if we have a practice ID
      if practice_id.present?
        patients_scope = patients_scope.joins(:practices_patients)
                                       .where(practices_patients: { practice_id: practice_id })
      end

      # Apply search and return results with additional patient information
      results = patients_scope
                .fuzzy_search(params[:q])
                .limit(20)
                .map do |patient|
                  {
                    id: patient.id,
                    text: patient.full_name_with_title_and_email,
                    name: patient.full_name,
                    email: patient.email,
                    date_of_birth: patient.date_of_birth,
                    age: patient.age_in_years_and_months,
                    address_line_1: patient.address_line_1,
                    address_line_2: patient.address_line_2,
                    city: patient.city,
                    postcode: patient.postcode,
                    full_address: patient.full_address,
                    default_practice_id: patient.current_practice&.id || patient.practices.first&.id
                  }
                end

      render json: { :results => results }, status: :ok
    end

    def bypass_2fa
      return if current_patient.blank?

      session[:two_factor_verified_at] = Time.current
      redirect_to after_sign_in_path_for(current_patient)
    end

    def toggle_archive
      # Get the status parameter from the request
      # For JSON requests, we need to parse the request body
      status_param = nil
      archived_param = nil

      if request.content_type =~ /json/
        # Parse JSON request body
        begin
          body = request.body.read
          json_params = JSON.parse(body) unless body.empty?
          status_param = json_params['status'] unless json_params.nil?
          archived_param = json_params['archived'] unless json_params.nil?
        rescue JSON::ParserError => e
          Rails.logger.error "Failed to parse JSON request body: #{e.message}"
        end
      else
        # Regular form parameters
        status_param = params[:status]
        archived_param = params[:archived]
      end

      # Handle new status parameter first
      if status_param.present?
        case status_param
        when 'active'
          update_attributes = { archived: false, on_stop: false }
        when 'archived'
          update_attributes = { archived: true, on_stop: false }
        when 'on_stop'
          update_attributes = { archived: false, on_stop: true }
        else
          respond_to do |format|
            format.json { render json: { success: false, error: 'Invalid status parameter' }, status: :bad_request }
            format.html { redirect_to admin_patient_path(@patient), alert: 'Invalid status parameter.' }
          end
          return
        end

        if @patient.update(update_attributes)
          respond_to do |format|
            format.json { render json: { success: true, status: @patient.patient_status } }
            format.html { redirect_to admin_patient_path(@patient), notice: 'Patient status updated successfully.' }
          end
        else
          respond_to do |format|
            format.json { render json: { success: false, error: 'Failed to update patient status' }, status: :unprocessable_entity }
            format.html { redirect_to admin_patient_path(@patient), alert: 'Failed to update patient status.' }
          end
        end
      # Fallback to legacy archived parameter for backward compatibility
      elsif !archived_param.nil?
        archived_status = ActiveModel::Type::Boolean.new.cast(archived_param)

        if @patient.update_column(:archived, archived_status)
          respond_to do |format|
            format.json { render json: { success: true, archived: @patient.archived } }
            format.html { redirect_to admin_patient_path(@patient), notice: 'Patient status updated successfully.' }
          end
        else
          respond_to do |format|
            format.json { render json: { success: false, error: 'Failed to update patient status' }, status: :unprocessable_entity }
            format.html { redirect_to admin_patient_path(@patient), alert: 'Failed to update patient status.' }
          end
        end
      else
        respond_to do |format|
          format.json { render json: { success: false, error: 'Missing status or archived parameter' }, status: :bad_request }
          format.html { redirect_to admin_patient_path(@patient), alert: 'Missing status or archived parameter.' }
        end
      end
    end

    def archive
      if @patient.archived
        @patient.update(archived: false)
        flash[:success] = 'Patient unarchived'
      else
        @patient.update(archived: true)
        flash[:success] = 'Patient archived'
      end
      redirect_to admin_patient_path(@patient)
    end

    def action_banner_status
      active_alerts = @patient.actions.where(action_type: 'alert', completed: false)
      active_complaints = @patient.actions.where(action_type: 'complaint', completed: false)

      render json: {
        alerts: {
          count: active_alerts.count,
          first_title: active_alerts.first&.title || active_alerts.first&.description
        },
        complaints: {
          count: active_complaints.count,
          first_title: active_complaints.first&.title || active_complaints.first&.description
        }
      }
    end

    def update_teethgrid_view
      if @patient.update(hide_base_and_history_treatments: params[:hide_base_and_history_treatments])
        render json: { success: true }
      else
        render json: { success: false, error: @patient.errors.full_messages }, status: :unprocessable_entity
      end
    end

    def update_field
      params_hash = patient_params.to_h

      if params_hash.present?
        field_name = params_hash.keys.first
        field_value = params_hash[field_name]

        consent_fields = %w[email_consent sms_consent whatsapp_consent instagram_consent facebook_consent marketing_consent]

        @patient.assign_attributes(field_name => field_value)
        @patient.consent_updated_at = Time.current if consent_fields.include?(field_name) && field_name != 'consent_updated_at'

        if @patient.save
          @patient.update_column(:temporary, false) if @patient.temporary?
          render json: { success: true, patient: { id: @patient.id, field_name => field_value } }
        else
          render json: { success: false, errors: ['Failed to update field'] }, status: :unprocessable_entity
        end
      else
        render json: { success: false, errors: ['No field provided for update'] }, status: :unprocessable_entity
      end
    end

    def add_payment_plan
      plan_ids = params[:patient][:cot_payment_plan_ids]

      if plan_ids.present?
        plan_id = plan_ids.first
        plan = CotPaymentPlan.find_by(id: plan_id)

        if plan && !@patient.cot_payment_plan_ids.include?(plan.id)
          @patient.cot_payment_plans << plan
          render json: {
            success: true,
            plan: {
              id: plan.id,
              name: plan.name,
              color: plan.color
            }
          }
        else
          render json: { success: false, message: 'Payment plan already added or not found' }
        end
      else
        render json: { success: false, message: 'No payment plan provided' }
      end
    end

    def remove_payment_plan
      plan_id = params[:plan_id]

      if plan_id.present?
        plan = CotPaymentPlan.find_by(id: plan_id)

        if plan && @patient.cot_payment_plan_ids.include?(plan.id.to_i)
          @patient.cot_payment_plans.delete(plan)
          render json: { success: true }
        else
          render json: { success: false, message: 'Payment plan not found or not assigned to patient' }
        end
      else
        render json: { success: false, message: 'No payment plan provided' }
      end
    end

    def update_assigned_staff
      # Get the assigned staff IDs from params
      staff_ids = params[:patient][:assigned_staff_ids] || []

      # Clear existing associations and create new ones directly
      ActiveRecord::Base.transaction do
        # Remove all existing associations
        @patient.patients_users.destroy_all

        # Fetch all users at once to avoid N+1 queries
        if staff_ids.any?
          users = User.where(id: staff_ids)

          # Create new associations for each selected staff member
          @patient.assigned_staff = users
        end
      end

      # Reload patient with eager loading to ensure we have the latest associations
      @patient.reload

      # Render the updated team members HTML
      team_html = render_to_string(partial: 'admin/patients/team_members', locals: { patient: @patient })
      render json: { success: true, message: 'Team members updated successfully', html: team_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update team members: #{e.message}" }, status: :unprocessable_entity
    end

    def remove_team_member
      user_id = params[:user_id]
      return render json: { success: false, message: 'User ID is required' }, status: :bad_request if user_id.blank?

      begin
        # Find the specific team member association and destroy it
        association = @patient.patients_users.find_by(user_id: user_id)

        if association
          association.destroy
          @patient.reload

          team_html = render_to_string(partial: 'admin/patients/team_members', locals: { patient: @patient })
          if @patient.assigned_staff.any?
            # Render the updated team members HTML
            render json: { success: true, message: 'Team member removed successfully', html: team_html, has_team_members: true }
          else
            # If no team members left, render the empty state
            render json: { success: true, message: 'Team member removed successfully', html: team_html, has_team_members: false }
          end
        else
          render json: { success: false, message: 'Team member not found for this patient' }, status: :not_found
        end
      rescue StandardError => e
        render json: { success: false, error: "Failed to remove team member: #{e.message}" }, status: :unprocessable_entity
      end
    end

    def update_assigned_practices
      # Get the assigned practice IDs from params
      practice_ids = params[:patient][:practice_ids] || []

      # Clear existing associations and create new ones directly
      ActiveRecord::Base.transaction do
        # Remove all existing associations
        @patient.practices_patients.destroy_all

        # Fetch all practices at once to avoid N+1 queries
        if practice_ids.any?
          practices = Practice.where(id: practice_ids)

          # Create new associations for all selected practices at once
          @patient.practices = practices
        end
      end

      # Reload patient with eager loading to ensure we have the latest associations
      @patient.reload

      # Render the updated practice members HTML
      practices_html = render_to_string(partial: 'admin/patients/practice_members', locals: { patient: @patient })
      render json: { success: true, message: 'Practices updated successfully', html: practices_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update practices: #{e.message}" }, status: :unprocessable_entity
    end

    # Search method moved/combined with the one at line ~257 that renders a partial

    def linked_family
      # For now, we'll return a mock response with sample data
      # In a real implementation, this would fetch actual linked family members from the database
      family_members = []

      # Render JSON response with family members
      render json: { success: true, family_members: family_members }
    rescue StandardError => e
      render json: { success: false, error: "Failed to fetch linked family: #{e.message}" }, status: :unprocessable_entity
    end

    def update_linked_family
      # Get the linked family IDs from params
      family_ids = params[:patient][:linked_family_ids] || []

      # In a real implementation, this would update the linked family relationships in the database
      # For now, we'll just return a success response with mock data

      # Create sample family members data for the response
      @linked_family_members = []
      if family_ids.any?
        # In a real implementation, this would fetch the actual patient records
        # For now, we'll create sample data based on the IDs
        family_ids.each_with_index do |id, index|
          @linked_family_members << {
            id: id,
            name: "Family Member #{index + 1}",
            dob_info: "01/01/2000 • #{25 - index}y 0m",
            initials: 'FM'
          }
        end
      end

      # Render the updated family members HTML
      family_html = render_to_string(partial: 'admin/patients/family_members', locals: { linked_family_members: @linked_family_members })
      render json: { success: true, message: 'Family members updated successfully', html: family_html }
    rescue StandardError => e
      render json: { success: false, error: "Failed to update family members: #{e.message}" }, status: :unprocessable_entity
    end

    # Handle login as patient request and trigger Pusher notification
    def available_devices
      practices = @patient.practices
      practices = Practice.all if practices.empty?

      current_practice = nil
      current_practice = @patient.current_practice if @patient.current_practice_id.present?

      practices << current_practice if current_practice && !practices.include?(current_practice)

      all_devices = []

      practices.each do |practice|
        practice_devices = practice.registered_devices
        all_devices.concat(practice_devices) if practice_devices.any?
      end

      patient_devices = @patient.registered_devices
      all_devices.concat(patient_devices) if patient_devices.any?

      devices = all_devices.uniq.sort_by(&:name)
      truly_available_count = devices.count(&:truly_available?)

      all_registered_devices = []
      practices.each do |practice|
        all_registered_devices.concat(practice.registered_devices)
      end
      all_registered_devices.concat(@patient.registered_devices)
      all_registered_devices.uniq.count

      # Return the devices as JSON with online status and additional info
      devices_json = devices.map do |device|
        device.as_json(only: %i[id name device_token last_used_at status created_at active]).merge(
          online: device.online?,
          truly_available: device.truly_available?
        )
      end

      render json: {
        success: true,
        devices: devices_json,
        total_registered_devices: devices.count,
        truly_available_count: truly_available_count,
        practice_id: practices.first&.id # Include practice ID for Pusher subscription
      }
    rescue StandardError => e
      render json: { success: false, message: e.message }, status: :internal_server_error
    end

    def login_as_patient
      # Get the selected device if provided
      device_id = params[:device_id]
      device = RegisteredDevice.find_by(id: device_id) if device_id.present?
      pusher = PusherService.new

      notification_data = {
        patient_id: @patient.id,
        patientId: @patient.id, # Add both formats for compatibility
        requested_by: current_user.id,
        requested_by_name: current_user.full_name,
        timestamp: Time.current.to_i
      }

      if device.present?
        # Send to specific device channel regardless of current status
        # This allows patient switching even if device is currently in use
        device_channel = "private-device-#{device.device_token}"
        notification_data[:device_id] = device.id
        notification_data[:device_token] = device.device_token

        # Log the patient switching attempt
        if device.patient_id.present? && device.patient_id != @patient.id
          Rails.logger.info("[ADMIN] Switching device #{device.id} from patient #{device.patient_id} to patient #{@patient.id}")
        end

        # Update device to be associated with the new patient and mark as in use
        # This will force a patient switch if needed
        device.update(patient_id: @patient.id, status: 'in_use', last_used_at: Time.current)

        # Trigger on device-specific channel
        pusher.trigger(device_channel, 'login_request', notification_data)

        flash[:notice] = "Login request sent to device: #{device.name}"
      else
        # Send to current device channel (any device on the sign-in page)
        notification_data[:send_to_current_device] = true

        # Trigger on current-device channel
        pusher.trigger('patient-signin-current-device', 'login_request', notification_data)

        flash[:notice] = 'Login request sent to any device currently on the sign-in page'
      end

      # Return success response
      render json: { success: true, message: 'Login request sent successfully' }
    rescue StandardError => e
      render json: { success: false, message: e.message }, status: :unprocessable_entity
    end

    def download_assets
      # Get selected labels from params
      selected_labels = params[:labels] || []

      if selected_labels.empty?
        flash[:error] = 'No labels selected for download.'
        redirect_to admin_patient_assets_path(@patient)
        return
      end

      assets_to_download = @patient.patient_assets.where(label: selected_labels).with_attached_file

      if assets_to_download.empty?
        flash[:notice] = 'No assets found for the selected labels for this patient.'
        redirect_to admin_patient_assets_path(@patient)
        return
      end

      patient_name_sanitized = @patient.full_name.gsub(/[^0-9A-Za-z.-]/, '_')
      timestamp = Time.current.strftime('%Y%m%d%H%M%S')
      zip_filename = "#{patient_name_sanitized}_assets_#{timestamp}.zip"

      # Create zip file in memory using StringIO
      zip_data = create_zip_from_assets(assets_to_download)

      # Send the data directly to the browser
      send_data zip_data,
                type: 'application/zip',
                disposition: 'attachment',
                filename: zip_filename

      # Explicitly return to prevent any further processing
      nil
    end

    def create_zip_from_assets(assets)
      # Create a new StringIO object to store the zip file
      zip_stream = StringIO.new

      # Create a new zip file in the StringIO
      Zip::OutputStream.write_buffer(zip_stream) do |zos|
        assets.each do |asset|
          next unless asset.file.attached?

          begin
            original_filename = asset.file.filename.to_s
            sanitized_label = asset.label.gsub(/[^0-9A-Za-z.-]/, '_')
            entry_name = File.join(sanitized_label, original_filename)

            zos.put_next_entry(entry_name)

            # Download the file content and write it to the zip
            asset.file.download do |chunk|
              zos.write(chunk)
            end
          rescue ActiveStorage::FileNotFoundError
            next
          rescue StandardError
            next
          end
        end
      end

      # Rewind the StringIO and return its content
      zip_stream.rewind
      zip_stream.read
    end

    # AJAX endpoint to fetch patient notes (archived or active)
    def notes
      archived = params[:archived] == 'true'
      pinned_only = params[:pinned_only] == 'true'

      # Eager load user associations and child notes to prevent N+1 queries
      # Only show parent notes (notes without patient_note_id) to match the main view
      notes = @patient.patient_notes.includes(:user, child_notes: :user)

      notes = if pinned_only
                # For pinned notes, get all pinned notes (root or child), then get their root notes
                pinned_notes = @patient.patient_notes.where(pinned: true, archived: false)
                root_note_ids = pinned_notes.map(&:root_note).map(&:id).uniq
                notes.where(id: root_note_ids)
              else
                notes.where(archived: archived, patient_note_id: nil)
              end

      notes = notes.order(created_at: :desc)

      respond_to do |format|
        format.json do
          render json: {
            success: true,
            notes: notes.map { |note| serialize_note_for_json(note) }
          }
        end
      end
    end

    private

    def serialize_note_for_json(note)
      # Determine which note to display as the main note (same logic as the Rails partial)
      # If this note has child notes (updates), show the most recent child note as main
      main_note = note.child_notes.any? ? note.child_notes.order(created_at: :desc).first : note
      root_note = note

      {
        id: main_note.id,
        root_id: root_note.id,
        title: main_note.title,
        text: main_note.text,
        color: main_note.color,
        pinned: root_note.pinned,
        archived: root_note.archived,
        user_name: main_note.user&.full_name || 'Admin User',
        created_at: main_note.created_at,
        has_updates: note.child_notes.any?,
        child_notes: serialize_child_notes(note),
        original_note: serialize_original_note(note)
      }
    end

    def serialize_child_notes(note)
      return [] unless note.child_notes.any?

      note.child_notes.order(created_at: :desc).map do |child_note|
        {
          id: child_note.id,
          title: child_note.title,
          text: child_note.text,
          user_name: child_note.user&.full_name || 'Admin User',
          created_at: child_note.created_at
        }
      end
    end

    def serialize_original_note(note)
      return nil unless note.child_notes.any?

      {
        id: note.id,
        title: note.title,
        text: note.text,
        user_name: note.user&.full_name || 'Admin User',
        created_at: note.created_at
      }
    end

    def create_temporary_patient_with_practice(practice)
      @patient = Patient.new(
        first_name: '',
        last_name: '',
        temporary: true,
        created_by_id: current_user.id,
        current_practice: practice
      )

      if @patient.save(validate: false)
        # Assign the practice to the patient
        @patient.practices << practice

        # Assign the current user as a team member
        @patient.assigned_staff << current_user

        # Assign default COT payment plan
        @patient.assign_default_cot_payment_plan
        @patient.save(validate: false) if @patient.changed?

        # Create a default course of treatment for immediate charting access
        CourseOfTreatment.create!(
          patient: @patient,
          dentist: current_user,
          lead_clinician: current_user,
          practice: practice,
          name: 'Default Course of Treatment',
          accepted: true
        )

        # Schedule cleanup job to run after 15 minutes
        CleanupTemporaryPatientsJob.set(wait: 15.minutes).perform_later(@patient.id)

        respond_to do |format|
          format.html { redirect_to admin_patient_path(@patient) }
          format.json { render json: { success: true, redirect_url: admin_patient_path(@patient) } }
        end
      else
        flash[:error] = ['Error creating temporary patient', @patient.errors.full_messages.join(', ')]
        respond_to do |format|
          format.html { redirect_to admin_patients_path }
          format.json { render json: { success: false, errors: @patient.errors.full_messages } }
        end
      end
    end

    def create_patient_with_practice(practice)
      @patient = Patient.new(patient_params)
      @patient.created_by_id = current_user.id
      @patient.temporary = false
      @patient.current_practice = practice

      if @patient.save
        # Assign the practice to the patient
        @patient.practices << practice

        # Assign the current user as a team member
        @patient.assigned_staff << current_user

        # Assign default COT payment plan
        @patient.assign_default_cot_payment_plan
        @patient.save if @patient.changed?

        respond_to do |format|
          format.html { redirect_to admin_patient_path(@patient) }
          format.json { render json: { success: true, redirect_url: admin_patient_path(@patient) } }
        end
      else
        respond_to do |format|
          format.html do
            flash[:error] = ['Error creating patient', @patient.errors.full_messages.join(', ')]
            redirect_to admin_patients_path
          end
          format.json { render json: { success: false, errors: @patient.errors.full_messages } }
        end
      end
    end

    def determine_practice_for_new_patient
      user_practices = current_user.practices

      # Single Practice Scenario: If user has access to only one practice, use it
      return user_practices.first if user_practices.count == 1

      # Current Practice Context: If user is viewing a specific practice, use it
      return Current.practice if Current.practice_id.present?

      # Multiple Practice Scenario: If viewing "all practices", return nil to trigger modal
      nil
    end

    def track_recently_viewed_patient(patient)
      # Don't track temporary patients
      return if patient.temporary?

      # Initialize session array if it doesn't exist
      session[:last_visited_patients] ||= []

      # Remove patient if already in the list (to avoid duplicates)
      session[:last_visited_patients].reject! { |id| id == patient.id }

      # Add patient to the beginning of the list
      session[:last_visited_patients].unshift(patient.id)

      # Keep only the 10 most recent patients
      session[:last_visited_patients] = session[:last_visited_patients].first(10)
    end

    def set_patient
      # Eager load necessary associations to prevent N+1 queries
      @patient = Patient.includes(:alerts, :cot_payment_plans, :patient_gp, practices_patients: :practice)
                        .find(params[:id])

      self.page_title = @patient.full_name_with_title
    end

    def set_users_and_practices
      @users = User.all
      @practices = current_user.practices
    end

    def set_alerts
      @alerts = policy_scope(Alert)
    end

    def set_gps
      @patient_gps = policy_scope(PatientGp)
    end

    def set_payment_plans
      @payment_plans = policy_scope(CotPaymentPlan)
    end

    def set_notes
      # Eager load user associations and child notes to prevent N+1 queries
      # Only show parent notes (notes without patient_note_id) in the main view
      @archived_notes = @patient.patient_notes.includes(:user, child_notes: :user).where(archived: true,
                                                                                         patient_note_id: nil).order(created_at: :desc)
      @archived_primary_notes = @archived_notes
      @notes = @patient.patient_notes.includes(:user, child_notes: :user).where(archived: false,
                                                                                patient_note_id: nil).order(created_at: :desc)
    end

    def patient_params
      params.require(:patient).permit(
        :title,
        :first_name,
        :middle_name,
        :last_name,
        :previous_last_name,
        :preferred_name,
        :date_of_birth,
        :email,
        :mobile_phone,
        :alternative_phone,
        :work_phone,
        :ethnicity,
        :biological_sex,
        :gender,
        :pronouns,
        :ni_number,
        :insurance_number,
        :address_line_1,
        :address_line_2,
        :county,
        :city,
        :postcode,
        :country,
        :school_name,
        :school_phone_number,
        :school_address_line_1,
        :school_address_line_2,
        :school_county,
        :school_country,
        :school_city,
        :school_postcode,
        :image,
        :sms_consent,
        :email_consent,
        :whatsapp_consent,
        :instagram_consent,
        :emergency_contact_name,
        :emergency_contact_number,
        :facebook_consent,
        :marketing_consent,
        :patient_gp_id,
        :occupation,
        :on_stop,
        assigned_staff_ids: [],
        practice_ids: [],
        alert_ids: [],
        cot_payment_plan_ids: []
      )
    end
  end
end
