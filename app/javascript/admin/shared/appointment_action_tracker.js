// Appointment Action Tracker
// Handles dynamic updates for actions assigned to appointments

class AppointmentActionTracker {
  constructor() {
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.refreshActionDisplays();
  }

  setupEventListeners() {
    // Listen for appointment status changes
    document.addEventListener('appointment:statusChanged', (event) => {
      this.handleAppointmentStatusChange(event.detail);
    });

    // Listen for appointment booking changes
    document.addEventListener('appointment:booked', (event) => {
      this.handleAppointmentBooked(event.detail);
    });

    document.addEventListener('appointment:unbooked', (event) => {
      this.handleAppointmentUnbooked(event.detail);
    });

    // Listen for appointment time changes
    document.addEventListener('appointment:timeChanged', (event) => {
      this.handleAppointmentTimeChange(event.detail);
    });

    // Refresh displays periodically
    setInterval(() => {
      this.refreshActionDisplays();
    }, 60000); // Every minute
  }

  handleAppointmentStatusChange(appointmentData) {
    const { appointmentId, status } = appointmentData;
    this.updateActionsForAppointment(appointmentId, { status });
  }

  handleAppointmentBooked(appointmentData) {
    const { appointmentId, patientId, startTime } = appointmentData;
    this.updateActionsForAppointment(appointmentId, { 
      booked: true, 
      patientId, 
      startTime 
    });
  }

  handleAppointmentUnbooked(appointmentData) {
    const { appointmentId } = appointmentData;
    this.updateActionsForAppointment(appointmentId, { 
      booked: false 
    });
  }

  handleAppointmentTimeChange(appointmentData) {
    const { appointmentId, startTime, endTime } = appointmentData;
    this.updateActionsForAppointment(appointmentId, { 
      startTime, 
      endTime 
    });
  }

  updateActionsForAppointment(appointmentId, updateData) {
    // Find all action elements for this appointment
    const actionElements = document.querySelectorAll(`[data-appointment-id="${appointmentId}"]`);
    
    actionElements.forEach(element => {
      this.updateActionElement(element, updateData);
    });

    // Also update sidebar actions
    this.updateSidebarActions(appointmentId, updateData);
  }

  updateActionElement(element, updateData) {
    const dueDateElement = element.querySelector('.action-due-date');
    const statusElement = element.querySelector('.action-status');
    
    if (dueDateElement) {
      this.updateDueDateDisplay(dueDateElement, updateData);
    }
    
    if (statusElement) {
      this.updateStatusDisplay(statusElement, updateData);
    }
  }

  updateDueDateDisplay(element, updateData) {
    if (updateData.booked === false) {
      // Appointment unbooked
      element.innerHTML = `
        <div class="flex flex-col">
          <span class="text-sm font-medium text-orange-600">Appointment unbooked</span>
          <span class="text-xs text-gray-500">Due when scheduled</span>
        </div>
      `;
    } else if (updateData.startTime) {
      // Appointment has a time
      const startTime = new Date(updateData.startTime);
      const formattedDate = startTime.toLocaleDateString('en-GB');
      const formattedTime = startTime.toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      element.innerHTML = `
        <div class="flex flex-col">
          <span class="text-sm font-medium">${formattedDate}</span>
          <span class="text-xs text-gray-500">${formattedTime}</span>
          <span class="text-xs text-blue-600">(${updateData.status || 'Scheduled'})</span>
        </div>
      `;
    }
  }

  updateStatusDisplay(element, updateData) {
    if (updateData.booked === false) {
      element.textContent = 'Appointment unbooked';
      element.className = 'text-sm text-orange-600';
    } else if (updateData.status) {
      element.textContent = updateData.status.charAt(0).toUpperCase() + updateData.status.slice(1);
      element.className = 'text-sm text-blue-600';
    }
  }

  updateSidebarActions(appointmentId, updateData) {
    const sidebarActions = document.querySelectorAll(`.actions-sidebar [data-appointment-id="${appointmentId}"]`);
    
    sidebarActions.forEach(action => {
      const badgeElement = action.querySelector('.action-due-badge');
      if (badgeElement) {
        this.updateSidebarBadge(badgeElement, updateData);
      }
    });
  }

  updateSidebarBadge(badgeElement, updateData) {
    if (updateData.booked === false) {
      badgeElement.className = 'text-xs font-medium px-2 py-1 rounded-full bg-orange-100 text-orange-700 border border-orange-200';
      badgeElement.innerHTML = '<i class="fa-light fa-tooth mr-1"></i>Appointment unbooked';
    } else if (updateData.startTime) {
      const startTime = new Date(updateData.startTime);
      const today = new Date();
      const diffTime = startTime - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      let badgeClass, statusText;
      
      if (diffDays < 0) {
        badgeClass = 'bg-red-100 text-red-700 border-red-200';
        statusText = `${Math.abs(diffDays)}d overdue`;
      } else if (diffDays === 0) {
        badgeClass = 'bg-orange-100 text-white border-orange-200';
        statusText = 'Due Today';
      } else {
        badgeClass = 'bg-blue-50 text-blue-600 border-blue-200';
        statusText = `Due in ${diffDays}d`;
      }
      
      badgeElement.className = `text-xs font-medium px-2 py-1 rounded-full ${badgeClass} border`;
      badgeElement.innerHTML = `<i class="fa-light fa-tooth mr-1"></i>${statusText}`;
    }
  }

  refreshActionDisplays() {
    // Refresh all appointment-assigned action displays
    const appointmentActions = document.querySelectorAll('[data-appointment-id]');
    
    appointmentActions.forEach(action => {
      const appointmentId = action.dataset.appointmentId;
      if (appointmentId) {
        this.fetchAppointmentStatus(appointmentId).then(data => {
          if (data) {
            this.updateActionElement(action, data);
          }
        });
      }
    });
  }

  async fetchAppointmentStatus(appointmentId) {
    try {
      const response = await fetch(`/admin/calendar_bookings/${appointmentId}/status.json`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching appointment status:', error);
    }
    return null;
  }

  // Utility method to trigger appointment events from other parts of the application
  static triggerAppointmentEvent(eventType, appointmentData) {
    const event = new CustomEvent(`appointment:${eventType}`, {
      detail: appointmentData
    });
    document.dispatchEvent(event);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.appointmentActionTracker = new AppointmentActionTracker();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AppointmentActionTracker;
}
