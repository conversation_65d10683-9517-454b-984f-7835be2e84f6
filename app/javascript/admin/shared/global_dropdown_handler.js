// CRM Dropdown System
// This handles CRM card dropdowns specifically to avoid conflicts with other dropdown systems

class CrmDropdownManager {
  constructor() {
    this.initialized = false;
    this.openDropdowns = new Set();
    this.init();
  }

  init() {
    if (this.initialized) return;

    // Clean up any existing inline styles on dropdown elements
    this.cleanupExistingDropdowns();

    // Use event delegation for better performance and dynamic content support
    document.addEventListener('click', this.handleClick.bind(this));
    document.addEventListener('keydown', this.handleKeydown.bind(this));

    this.initialized = true;
  }

  cleanupExistingDropdowns() {
    // Only handle CRM card dropdowns to avoid conflicts with other systems
    const allDropdowns = document.querySelectorAll('[id^="card-dropdown-"]');

    allDropdowns.forEach(dropdown => {
      // Clear any inline styles that might interfere with CSS classes
      dropdown.style.display = '';
      dropdown.style.visibility = '';

      // Ensure they start in a closed state
      dropdown.classList.add('hidden');
    });
  }

  handleClick(event) {
    const dropdownToggle = event.target.closest('[data-dropdown-toggle]');

    if (dropdownToggle) {
      const targetId = dropdownToggle.getAttribute('data-dropdown-toggle');

      // Only handle CRM card dropdowns to avoid conflicts with navbar and other systems
      if (!targetId.startsWith('card-dropdown-')) {
        return; // Let other systems handle their own dropdowns
      }

      event.preventDefault();
      event.stopPropagation();

      const targetDropdown = document.getElementById(targetId);

      if (!targetDropdown) {
        return;
      }

      const isCurrentlyOpen = this.isDropdownOpen(targetDropdown);

      // Close all other dropdowns first
      this.closeAllDropdowns(targetId);

      // Toggle the clicked dropdown
      if (!isCurrentlyOpen) {
        this.openDropdown(targetDropdown, targetId);
      } else {
        // If it was open, close it (true toggle behavior)
        this.closeDropdown(targetDropdown, targetId);
      }

    } else {
      // Check if click is inside any CRM card dropdown
      const clickedInsideDropdown = event.target.closest('[id^="card-dropdown-"]');

      if (!clickedInsideDropdown) {
        this.closeAllDropdowns();
      }
    }
  }

  handleKeydown(event) {
    if (event.key === 'Escape') {
      this.closeAllDropdowns();
    }
  }

  isDropdownOpen(dropdown) {
    return !dropdown.classList.contains('hidden');
  }

  openDropdown(dropdown, dropdownId) {
    // Use setTimeout to ensure this runs after any other event handlers
    setTimeout(() => {
      dropdown.classList.remove('hidden');
      dropdown.style.display = '';
      dropdown.style.visibility = '';
      this.openDropdowns.add(dropdownId);
    }, 0);
  }

  closeDropdown(dropdown, dropdownId) {
    dropdown.classList.add('hidden');
    dropdown.style.display = '';
    dropdown.style.visibility = '';
    this.openDropdowns.delete(dropdownId);
  }

  closeAllDropdowns(exceptId = null) {
    // Only handle CRM card dropdowns to avoid conflicts with other systems
    const allDropdowns = document.querySelectorAll('[id^="card-dropdown-"]');

    allDropdowns.forEach(dropdown => {
      if (exceptId && dropdown.id === exceptId) {
        return; // Skip the dropdown we're trying to keep open
      }

      // Always close and clear any inline styles that might interfere
      this.closeDropdown(dropdown, dropdown.id);
    });

    // Clear the set if no exception
    if (!exceptId) {
      this.openDropdowns.clear();
    }
  }

  // Public API for manual control
  toggle(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return false;

    if (this.isDropdownOpen(dropdown)) {
      this.closeDropdown(dropdown, dropdownId);
    } else {
      this.closeAllDropdowns(dropdownId);
      this.openDropdown(dropdown, dropdownId);
    }
    return true;
  }

  open(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return false;

    this.closeAllDropdowns(dropdownId);
    this.openDropdown(dropdown, dropdownId);
    return true;
  }

  close(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return false;

    this.closeDropdown(dropdown, dropdownId);
    return true;
  }
}

// Initialize the CRM dropdown manager
const crmDropdownManager = new CrmDropdownManager();

// Make it globally available
window.crmDropdownManager = crmDropdownManager;
window.closeAllCrmDropdowns = () => crmDropdownManager.closeAllDropdowns();

// Legacy support for existing functions (only for CRM dropdowns)
window.toggleCrmDropdown = (id) => crmDropdownManager.toggle(id);
