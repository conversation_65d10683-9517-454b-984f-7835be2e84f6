// app/javascript/admin/conversations/messages_list.js

// Function to initialize messages list functionality (available globally for AJAX reloads)
function initializeMessagesList() {
  if ($('#conversation-tabs').length === 0) {
    return;
  }

  // Tab handling functionality
  function handleTabClick(tabName, button) {
    // Store the active tab in localStorage
    localStorage.setItem('activeConversationTab', tabName);

    // Reset all tabs
    const allButtons = document.querySelectorAll('#conversation-tabs .tab-button');
    const allContents = document.querySelectorAll('#conversation-tabs .tab-content');

    // Reset all buttons
    allButtons.forEach(btn => {
      btn.classList.remove('active-tab');

      if (btn.classList.contains('bg-gradient-to-r')) {
        btn.classList.remove('bg-gradient-to-r');

        // Remove specific gradient classes based on tab type
        if (btn.getAttribute('data-tab') === 'attention') {
          btn.classList.remove('from-rose-200', 'to-rose-300', 'text-rose-800');
        } else if (btn.getAttribute('data-tab') === 'recent') {
          btn.classList.remove('from-blue-200', 'to-blue-300', 'text-blue-800');
        } else if (btn.getAttribute('data-tab') === 'older') {
          btn.classList.remove('from-amber-200', 'to-amber-300', 'text-amber-800');
        } else if (btn.getAttribute('data-tab') === 'all') {
          btn.classList.remove('from-emerald-200', 'to-emerald-300', 'text-emerald-800');
        }

        btn.classList.add('bg-white', 'text-gray-600');
      }

      // Collapse text
      const textSpan = btn.querySelector('.tab-text');
      if (textSpan && !textSpan.classList.contains('opacity-0')) {
        textSpan.classList.add('opacity-0', 'w-0');
        btn.classList.remove('min-w-[130px]');
        btn.classList.add('min-w-[36px]');
      }
    });

    // Hide all content sections
    allContents.forEach(content => {
      content.classList.add('hidden');
    });

    // Show the corresponding content
    const contentId = `${tabName}-conversations-container`;
    const content = document.getElementById(contentId);
    if (content) {
      content.classList.remove('hidden');

      // Reset pagination for the tab
      if (window.resetTabPagination) {
        window.resetTabPagination(content);
      }
    }

    // Style the active button
    button.classList.add('active-tab');
    button.classList.remove('bg-white', 'text-gray-600');

    // Add gradient styling based on tab type
    if (tabName === 'attention') {
      button.classList.add('bg-gradient-to-r', 'from-rose-200', 'to-rose-300', 'text-rose-800');
    } else if (tabName === 'recent') {
      button.classList.add('bg-gradient-to-r', 'from-blue-200', 'to-blue-300', 'text-blue-800');
    } else if (tabName === 'older') {
      button.classList.add('bg-gradient-to-r', 'from-amber-200', 'to-amber-300', 'text-amber-800');
    } else if (tabName === 'all') {
      button.classList.add('bg-gradient-to-r', 'from-emerald-200', 'to-emerald-300', 'text-emerald-800');
    }

    // Expand the text for the active button
    setTimeout(() => {
      const textSpan = button.querySelector('.tab-text');
      if (textSpan) {
        textSpan.classList.remove('opacity-0', 'w-0');
        button.classList.remove('min-w-[36px]');
        button.classList.add('min-w-[130px]');
      }
    }, 10);
  }

  // Make handleTabClick globally available
  window.handleTabClick = handleTabClick;

  // Infinite scroll pagination functionality
  class ConversationPagination {
    constructor() {
      this.containers = new Map();
      this.perPage = 20; // PER_PAGE from service
      this.init();
    }

    init() {
      // Initialize all tab containers
      const tabContainers = document.querySelectorAll('[data-controller="infinite-scroll"]');

      tabContainers.forEach(container => {
        const tabName = this.getTabNameFromContainer(container);
        this.containers.set(tabName, {
          element: container,
          page: parseInt(container.dataset.infiniteScrollPageValue) || 1,
          loading: false,
          hasMore: true,
          url: container.dataset.infiniteScrollUrlValue,
          cardsContainer: container.querySelector('.conversation-cards-container')
        });

        this.setupScrollListener(container, tabName);
      });
    }

    getTabNameFromContainer(container) {
      const id = container.id;
      return id.replace('-conversations-container', '');
    }

    setupScrollListener(container, tabName) {
      container.addEventListener('scroll', () => {
        if (this.shouldLoadMore(container, tabName)) {
          this.loadMoreConversations(tabName);
        }
      });
    }

    shouldLoadMore(container, tabName) {
      const containerData = this.containers.get(tabName);

      if (!containerData || containerData.loading || !containerData.hasMore) {
        return false;
      }

      // Check if we're near the bottom (within 100px)
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;

      return scrollTop + clientHeight >= scrollHeight - 100;
    }

    async loadMoreConversations(tabName) {
      const containerData = this.containers.get(tabName);
      if (!containerData || containerData.loading) return;

      containerData.loading = true;
      this.showLoader(containerData.element);

      try {
        const nextPage = containerData.page + 1;
        const url = new URL(containerData.url, window.location.origin);
        url.searchParams.set('page', nextPage);
        url.searchParams.set('format', 'json');

        const response = await fetch(url, {
          headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.conversations && data.conversations.length > 0) {
          this.appendConversations(containerData, data.conversations, tabName);
          containerData.page = nextPage;

          // Check if we have more pages
          if (data.conversations.length < this.perPage || !data.has_more) {
            containerData.hasMore = false;
          }
        } else {
          containerData.hasMore = false;
        }

      } catch (error) {
        console.error('Error loading more conversations:', error);
        this.showError(containerData.element);
      } finally {
        containerData.loading = false;
        this.hideLoader(containerData.element);
      }
    }

    appendConversations(containerData, conversations, tabName) {
      const cardsContainer = containerData.cardsContainer;
      if (!cardsContainer) return;

      conversations.forEach(conversation => {
        const conversationCard = this.createConversationCard(conversation, tabName);
        cardsContainer.appendChild(conversationCard);
      });
    }

    createConversationCard(conversation, tabName) {
      const cardElement = document.createElement('div');
      cardElement.innerHTML = conversation.html;
      return cardElement.firstElementChild;
    }

    showLoader(container) {
      const loader = container.querySelector('.pagination-loader');
      if (loader) {
        loader.classList.remove('hidden');
      }
    }

    hideLoader(container) {
      const loader = container.querySelector('.pagination-loader');
      if (loader) {
        loader.classList.add('hidden');
      }
    }

    showError(container) {
      // Create error message if it doesn't exist
      let errorElement = container.querySelector('.pagination-error');
      if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'pagination-error text-center py-3 text-red-500 text-sm';
        errorElement.innerHTML = 'Error loading conversations. <button class="text-red-600 underline ml-1" onclick="location.reload()">Refresh page</button>';
        container.appendChild(errorElement);
      }
      errorElement.classList.remove('hidden');
    }

    resetTabPagination(container) {
      const tabName = this.getTabNameFromContainer(container);
      const containerData = this.containers.get(tabName);

      if (containerData) {
        containerData.page = 1;
        containerData.loading = false;
        containerData.hasMore = true;

        // Hide any error messages
        const errorElement = container.querySelector('.pagination-error');
        if (errorElement) {
          errorElement.classList.add('hidden');
        }
      }
    }
  }

  // Initialize pagination system
  const conversationPagination = new ConversationPagination();

  // Global function for resetting tab pagination
  window.resetTabPagination = function(container) {
    conversationPagination.resetTabPagination(container);
  };

  // Message pagination functionality
  class MessagePagination {
    constructor() {
      this.chatContainer = document.getElementById('chat-container');
      this.chatMessages = document.getElementById('chat-messages');
      this.loader = document.getElementById('pagination-loader');

      if (this.chatContainer && this.chatMessages) {
        this.conversationId = this.chatMessages.dataset.conversationId;
        this.currentPage = parseInt(this.chatMessages.dataset.currentPage) || 1;
        this.hasMore = this.chatMessages.dataset.hasMore === 'true';
        this.isLoading = false;
        this.isAppending = false; // Flag to prevent scroll triggers during message appending

        this.init();
      }
    }

    init() {
      this.attachScrollListener();
    }

    attachScrollListener() {
      this.chatContainer.addEventListener('scroll', () => {
        if (this.shouldLoadMore() && !this.isAppending) {
          this.loadMoreMessages();
        }
      });
    }

    shouldLoadMore() {
      if (this.isLoading || !this.hasMore) {
        return false;
      }

      // For flex-col-reverse, when visually at the "top" (oldest messages),
      // we're actually at the maximum scroll position
      const threshold = 50;
      const maxScrollTop = this.chatContainer.scrollHeight - this.chatContainer.clientHeight;
      const isNearTop = this.chatContainer.scrollTop <= threshold;

      return isNearTop;
    }

    async loadMoreMessages() {
      if (this.isLoading || !this.hasMore) {
        return;
      }

      this.isLoading = true;
      this.showLoader();

      try {
        const nextPage = this.currentPage + 1;
        const response = await fetch(`/admin/conversations/${this.conversationId}/messages?page=${nextPage}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          }
        });

        if (!response.ok) {
          throw new Error('Network response was not ok');
        }

        const data = await response.json();

        this.appendMessages(data.messages);
        this.currentPage = data.page;
        this.hasMore = data.has_more;

        this.chatMessages.dataset.currentPage = this.currentPage;
        this.chatMessages.dataset.hasMore = this.hasMore;

        tippy("[data-tippy-content]", {
          allowHTML: true,
          theme: "light"
        });

        const filterButton = document.querySelectorAll('.filter-button.active-filter')[0];

        const filterType = filterButton.dataset.filterType;
        const messageContainers = document.querySelectorAll('#chat-messages > div');

        if (filterType === 'all') {
          // Show all messages
          messageContainers.forEach(container => {
            container.style.display = '';
          });
        } else {
          // Filter messages by type
          messageContainers.forEach(container => {
            const messageBubble = container.querySelector('.message-bubble');
            if (messageBubble && messageBubble.dataset.messageType === filterType) {
              container.style.display = '';
            } else {
              container.style.display = 'none';
            }
          });
        }

      } catch (error) {
        console.error('Error loading more messages:', error);
        this.showError();
      } finally {
        this.isLoading = false;
        this.hideLoader();
      }
    }

    appendMessages(messages) {
      if (!messages || messages.length === 0) {
        return;
      }

      // Store current scroll position relative to the bottom
      const oldScrollHeight = this.chatContainer.scrollHeight;
      const scrollBottom = oldScrollHeight - this.chatContainer.scrollTop;

      this.isAppending = true;
      messages.forEach((messageData, index) => {
        const messageElement = this.createMessageElement(messageData.html);

        if (messageElement) {
          // For flex-col-reverse, append at the end (which appears at the top visually)
          this.chatMessages.appendChild(messageElement);
        } else {
        }
      });

      // Maintain scroll position relative to the bottom content
      const newScrollHeight = this.chatContainer.scrollHeight;
      this.chatContainer.scrollTop = newScrollHeight - scrollBottom;

      // Re-enable scroll detection after a brief delay to allow DOM to settle
      setTimeout(() => {
        this.isAppending = false;
      }, 100);
    }

    createMessageElement(html) {
      const wrapper = document.createElement('div');
      wrapper.innerHTML = html.trim();
      // Return first element child (skips comment nodes)
      return wrapper.firstElementChild;
    }

    showLoader() {
      if (this.loader) {
        this.loader.classList.remove('hidden');
      }
    }

    hideLoader() {
      if (this.loader) {
        this.loader.classList.add('hidden');
      }
    }

    showError() {
    }
  }

  // Initialize message pagination system
  const chatContainer = document.getElementById('chat-container');
  const chatMessages = document.getElementById('chat-messages');

  if (chatContainer && chatMessages) {
    const messagePagination = new MessagePagination();

    // Start at the bottom (newest messages) immediately without animation
    // With flex-col-reverse, newest messages are visually at bottom = scrollHeight
    chatContainer.scrollTop = chatContainer.scrollHeight;
  }

  // Patient search functionality
  const searchInput = document.getElementById('patient-search');
  const patientList = document.getElementById('patient-list');
  const searchLoading = document.getElementById('search-loading');
  let searchTimeout;

  if (searchInput) {
    searchInput.addEventListener('input', function(e) {
      clearTimeout(searchTimeout);
      const query = e.target.value.trim();

      if (query.length < 2) {
        patientList.innerHTML = '<div class="text-center py-6 text-gray-500">' +
          (query.length === 0 ? 'Start typing to search for patients...' : 'Type at least 2 characters...') +
          '</div>';
        return;
      }

      searchLoading.classList.remove('hidden');

      searchTimeout = setTimeout(() => {
        fetch(`/admin/conversations/search_patients?q=${encodeURIComponent(query)}`)
          .then(response => response.text())
          .then(html => {
            patientList.innerHTML = html;
            searchLoading.classList.add('hidden');
          })
          .catch(error => {
            console.error('Error searching patients:', error);
            patientList.innerHTML = '<div class="text-center py-6 text-red-500">Error loading patients. Please try again.</div>';
            searchLoading.classList.add('hidden');
          });
      }, 300);
    });
  }

  // Handle patient selection
  window.selectPatient = function(element, patient) {
    const patientIdInput = document.getElementById('conversation_patient_id');
    if (patientIdInput) {
      patientIdInput.value = patient.id;
    }

    const patientNameDisplay = document.getElementById('patient-name-display');
    if (patientNameDisplay) {
      patientNameDisplay.textContent = patient.full_name;
    }

    const modal = document.getElementById('patient-selection-modal');
    if (modal) {
      modal.classList.add('hidden');
    }

    const teamModal = document.getElementById('team-message-modal');
    if (teamModal) {
      teamModal.classList.remove('hidden');
    }
  };

  // Modal closing functions
  window.closePatientModal = function(event) {
    if (event.target === document.getElementById('patient-selection-modal')) {
      document.getElementById('patient-selection-modal').classList.add('hidden');
    }
  };

  window.closeTeamModal = function(event) {
    if (event.target === document.getElementById('team-message-modal')) {
      document.getElementById('team-message-modal').classList.add('hidden');
    }
  };

  // New conversation dropdown functionality
  const newConversationButton = document.getElementById('new-conversation-button');
  const conversationDropdown = document.getElementById('new-conversation-dropdown');
  const patientOption = document.getElementById('patient-communication-option');
  const teamOption = document.getElementById('team-message-option');
  const patientModal = document.getElementById('patient-selection-modal');
  const teamModal = document.getElementById('team-message-modal');

  if (newConversationButton && conversationDropdown) {
    newConversationButton.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      const buttonRect = newConversationButton.getBoundingClientRect();

      conversationDropdown.style.position = 'fixed';
      conversationDropdown.style.left = buttonRect.left + 'px';
      conversationDropdown.style.top = (buttonRect.bottom + 8) + 'px';
      conversationDropdown.style.zIndex = '9999';

      conversationDropdown.classList.remove('hidden');

      setTimeout(function() {
        document.addEventListener('click', function closeDropdown(e) {
          if (!conversationDropdown.contains(e.target) && e.target !== newConversationButton) {
            conversationDropdown.classList.add('hidden');
            document.removeEventListener('click', closeDropdown);
          }
        });
      }, 10);
    });

    // Patient option click handler
    if (patientOption && patientModal) {
      patientOption.addEventListener('click', function() {
        conversationDropdown.classList.add('hidden');
        patientModal.classList.remove('hidden');
      });

      // Close patient modal
      const patientCloseButtons = document.querySelectorAll('.patient-modal-close');
      patientCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
          patientModal.classList.add('hidden');
        });
      });
    }

    // Team option click handler
    if (teamOption && teamModal) {
      teamOption.addEventListener('click', function() {
        conversationDropdown.classList.add('hidden');
        teamModal.classList.remove('hidden');
      });

      // Close team modal
      const teamCloseButtons = document.querySelectorAll('.team-modal-close');
      teamCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
          teamModal.classList.add('hidden');
        });
      });
    }
  }

  const $searchInput = $('#conversation-search');
  if ($searchInput.length > 0) {
    const $newSearchInput = $searchInput.clone();
    $searchInput.replaceWith($newSearchInput);

    let searchTimeout;
    $newSearchInput.on('input', function() {
      clearTimeout(searchTimeout);
      const searchTerm = $(this).val().trim();

      searchTimeout = setTimeout(function() {
        performBackendSearch(searchTerm);
      }, 300);
    });
  }

  function performBackendSearch(searchTerm) {
    const $activeTabContent = $('.tab-content:not(.hidden)');
    const activeTab = $activeTabContent.attr('id').replace('-conversations-container', '');

    $activeTabContent.html(`
      <div class="py-8 text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
          <svg class="animate-spin h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <p class="text-sm text-gray-500">Searching conversations...</p>
      </div>
    `);

    $.ajax({
      url: window.location.pathname,
      method: 'GET',
      data: {
        filter: activeTab,
        search: searchTerm,
        page: 1
      },
      dataType: 'html',
      success: function(data) {
        const $response = $(data);
        const $conversationCards = $response.find('.conversation-card');

        if ($conversationCards.length === 0) {
          $activeTabContent.html(`
            <div class="py-8 text-center">
              <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x h-6 w-6 text-gray-500">
                  <path d="m13.5 8.5-5 5"></path>
                  <path d="m8.5 8.5 5 5"></path>
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.3-4.3"></path>
                </svg>
              </div>
              <p class="text-sm font-medium text-gray-900 mb-1">No conversations found</p>
              <p class="text-xs text-gray-500">Try adjusting your search terms</p>
            </div>
          `);
        } else {
          $activeTabContent.html('');
          $conversationCards.each(function() {
            $activeTabContent.append($(this));
          });

          $activeTabContent.addClass('space-y-1.5');
        }
      },
      error: function(xhr, status, error) {
        console.error('Search error:', error);
        $activeTabContent.html(`
          <div class="py-8 text-center">
            <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-circle h-6 w-6 text-red-500">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-900 mb-1">Search failed</p>
            <p class="text-xs text-gray-500">Please try again</p>
          </div>
        `);
      }
    });
  }

  // Initialize stored active tab
  const storedActiveTab = localStorage.getItem('activeConversationTab');
  if (storedActiveTab) {
    const storedTabButton = document.querySelector(`.tab-btn-${storedActiveTab}`);
    if (storedTabButton) {
      handleTabClick(storedActiveTab, storedTabButton);
    } else {
      const fallbackButton = document.querySelector(`[data-tab="${storedActiveTab}"]`);
      if (fallbackButton) {
        handleTabClick(storedActiveTab, fallbackButton);
      }
    }
  }
}

// Make messages list initialization available globally
window.initializeMessagesList = initializeMessagesList;

document.addEventListener('DOMContentLoaded', initializeMessagesList);
