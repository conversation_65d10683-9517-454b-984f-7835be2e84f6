// Patient Actions Filtering and Management
console.log('=== PATIENT ACTIONS JS LOADED ===');

// Check if jQuery is available
if (typeof $ === 'undefined') {
  console.error('jQuery is not defined! Actions.js requires jQuery.');
} else {
  console.log('jQuery is available:', $.fn.jquery);
}

// Namespace for patient actions to avoid conflicts with sidebar
window.PatientActions = window.PatientActions || {};

// Global state tracking for filters - namespaced
window.PatientActions.currentDateFilter = 'all';   // today, all, completed
window.PatientActions.currentTypeFilter = 'all';   // all, task, reminder, alerts, callback, complaint

$(document).ready(function() {
    console.log('=== PATIENT ACTIONS PAGE ===');

    // Only run on patient actions pages - check for specific actions page elements
    if (!$('.admin-table-row[data-action-id]').length && !$('#patientNewActionModal').length && !$('#actions-container').length) {
        console.log('Not on patient actions page, exiting');
        return;
    }

    console.log('Starting with filters:', {
        currentDateFilter: window.PatientActions.currentDateFilter,
        currentTypeFilter: window.PatientActions.currentTypeFilter
    });

    // Initialize the page
    initializePage();

    function initializePage() {
        console.log('=== INITIALIZING PAGE ===');
        console.log('Default filters on load:', {
            currentDateFilter: window.PatientActions.currentDateFilter,
            currentTypeFilter: window.PatientActions.currentTypeFilter
        });

        // Initialize tooltips for user avatars
        initializeTooltips();

        // Initialize tooltips for user avatars
        initializeTooltips();

        // Count initial actions
        const totalRows = $('.admin-table-row').length;
        const completedRows = $('.admin-table-row').filter(function() {
            return $(this).data('action-completed') === true || $(this).data('action-completed') === 'true';
        }).length;

        console.log('Initial action counts:', {
            total: totalRows,
            completed: completedRows,
            nonCompleted: totalRows - completedRows
        });

        // Fix status text for existing completed actions
        fixExistingCompletedActions();

        // Show all actions initially, then apply default filters
        $('.admin-table-row').show();

        // Initialize the default "All" filter button with proper styling
        initializeDefaultFilterButton();

        // Apply default filters (today + all types)
        console.log('Applying initial filters...');
        applyFilters();

        console.log('Page initialized with default filters');
    }

    function initializeTooltips() {
        console.log('=== INITIALIZING TOOLTIPS ===');

        // Initialize tooltips for user avatars
        if (typeof tippy !== 'undefined') {
            tippy('[data-tippy-content]', {
                theme: 'light',
                placement: 'top',
                arrow: true,
                delay: [300, 100],
                duration: [200, 150],
                zIndex: 9999
            });
            console.log('Tippy tooltips initialized');
        } else {
            console.warn('Tippy.js not available for tooltips');
        }
    }

    function fixExistingCompletedActions() {
        console.log('=== FIXING EXISTING COMPLETED ACTIONS ===');

        $('.admin-table-row').each(function(index) {
            const $row = $(this);
            const isCompleted = $row.data('action-completed') === true || $row.data('action-completed') === 'true';

            if (isCompleted) {
                const actionType = $row.data('action-type');
                const actionId = $row.data('action-id');
                const statusText = getCompletionStatusText(actionType);

                console.log(`\n=== FIXING ACTION ${actionId} (${actionType}) ===`);
                console.log('Target status text:', statusText);

                // Debug: Log the entire row HTML structure
                console.log('Row HTML:', $row[0].outerHTML.substring(0, 500) + '...');

                // 1. Update icon label (first column)
                const $iconArea = $row.find('td:first-child');
                console.log('Icon area found:', $iconArea.length);
                const $iconSpan = $iconArea.find('span');
                console.log('Icon spans found:', $iconSpan.length);
                $iconSpan.each(function(i) {
                    const currentText = $(this).text();
                    console.log(`Icon span ${i}: "${currentText}"`);
                    if (currentText === 'Completed' || currentText === 'Resolved') {
                        console.log(`Updating icon span ${i} from "${currentText}" to "${statusText}"`);
                        $(this).text(statusText);
                    }
                });

                // 2. Update status badge (third column)
                const $badges = $row.find('div').filter(function() {
                    return $(this).text() === 'Completed' || $(this).text() === 'Resolved';
                });
                console.log('Status badges found:', $badges.length);
                $badges.each(function(i) {
                    const currentText = $(this).text();
                    console.log(`Updating badge ${i} from "${currentText}" to "${statusText}"`);
                    $(this).text(statusText);
                });

                // 3. Update due date text (fourth column)
                const $dueDateArea = $row.find('td:nth-child(4)');
                console.log('Due date area found:', $dueDateArea.length);
                const $dueDateSpans = $dueDateArea.find('span');
                console.log('Due date spans found:', $dueDateSpans.length);
                $dueDateSpans.each(function(i) {
                    const currentHtml = $(this).html();
                    console.log(`Due date span ${i} HTML:`, currentHtml);
                    if (currentHtml && (currentHtml.includes('Completed') || currentHtml.includes('Resolved'))) {
                        const newHtml = currentHtml.replace(/Completed|Resolved/, statusText);
                        console.log(`Updating due date span ${i} to:`, newHtml);
                        $(this).html(newHtml);
                    }
                });

                // 4. Update button text (actions column)
                const $buttons = $row.find('button');
                console.log('Buttons found:', $buttons.length);
                $buttons.each(function(i) {
                    const currentHtml = $(this).html();
                    if (currentHtml && (currentHtml.includes('Completed') || currentHtml.includes('Resolved'))) {
                        const newHtml = currentHtml.replace(/Completed|Resolved/, statusText);
                        console.log(`Updating button ${i} from "${currentHtml}" to "${newHtml}"`);
                        $(this).html(newHtml);
                    }
                });

                console.log(`=== FINISHED FIXING ACTION ${actionId} ===\n`);
            }
        });

        console.log('Finished fixing existing completed actions');

        // Precise fix for existing completed actions based on HTML structure analysis
        $('.admin-table-row').each(function() {
            const $row = $(this);
            const actionType = $row.data('action-type');

            if (actionType === 'complaint' || actionType === 'alert') {
                const actionId = $row.data('action-id');
                const statusText = getCompletionStatusText(actionType);

                console.log(`Precisely fixing ${actionType} action ${actionId}: "${statusText}"`);

                // 1. Fix status badge (2nd column) - emerald badge with "Completed" text
                const $statusBadge = $row.find('span.bg-emerald-100.text-emerald-800');
                if ($statusBadge.length && $statusBadge.text().trim() === 'Completed') {
                    console.log('Updating status badge from "Completed" to "' + statusText + '"');
                    $statusBadge.text(statusText);
                }

                // 2. Fix status column (4th column) - plain text "Completed"
                const $statusColumn = $row.find('td').eq(3); // 4th column (0-indexed)
                if ($statusColumn.length && $statusColumn.text().trim() === 'Completed') {
                    console.log('Updating status column from "Completed" to "' + statusText + '"');
                    $statusColumn.text(statusText);
                }

                // 3. Fix any other direct text nodes containing "Completed"
                $row.contents().filter(function() {
                    return this.nodeType === 3 && this.textContent.includes('Completed');
                }).each(function() {
                    console.log('Updating text node from "' + this.textContent + '" to "' + this.textContent.replace('Completed', statusText) + '"');
                    this.textContent = this.textContent.replace('Completed', statusText);
                });
            }
        });
    }

    function initializeDefaultFilterButton() {
        console.log('=== INITIALIZING DEFAULT FILTER BUTTON ===');

        // Find the "All" button and apply the indigo gradient
        const $allButton = $('#all-action-tab');
        if ($allButton.length) {
            const gradients = {
                'indigo': 'linear-gradient(to right, #c7d2fe, #a5b4fc)',
                'blue': 'linear-gradient(to right, #dbeafe, #bfdbfe)',
                'amber': 'linear-gradient(to right, #fef3c7, #fde68a)',
                'red': 'linear-gradient(to right, #fecaca, #fca5a5)',
                'green': 'linear-gradient(to right, #d1fae5, #a7f3d0)',
                'purple': 'linear-gradient(to right, #e9d5ff, #d8b4fe)'
            };

            // Apply the indigo gradient to the "All" button
            $allButton.css('background', gradients['indigo']);
            console.log('Applied indigo gradient to All button');
        }
    }



    // Handle date filter buttons (Today, All Actions, Completed) - PATIENT ACTIONS PAGE ONLY
    // Use document selector since these buttons are outside #actions-container
    $(document).on('click', '.patient-top-filter-btn', function(e) {
        e.preventDefault();
        console.log('=== PATIENT ACTIONS DATE FILTER CLICKED ===');

        const clickedFilter = $(this).data('filter');
        const clickedColor = $(this).data('color');
        console.log('Clicked filter:', clickedFilter, 'Color:', clickedColor);
        console.log('Button element:', this);
        console.log('Button ID:', $(this).attr('id'));

        window.PatientActions.currentDateFilter = clickedFilter;

        // Manually handle Apple-style styling
        handleTopFilterStyling($(this), clickedColor);

        console.log('Updated currentDateFilter to:', window.PatientActions.currentDateFilter);
        applyFilters();
    });

    function handleTopFilterStyling($clickedButton, color) {
        // Color gradients
        const colorGradients = {
            'blue': 'linear-gradient(to right, #bfdbfe, #93c5fd)',
            'purple': 'linear-gradient(to right, #ddd6fe, #c4b5fd)',
            'green': 'linear-gradient(to right, #bbf7d0, #86efac)'
        };

        const textColors = {
            'blue': 'text-blue-800',
            'purple': 'text-purple-800',
            'green': 'text-green-800'
        };

        const iconColors = {
            'blue': 'text-blue-700',
            'purple': 'text-purple-700',
            'green': 'text-green-700'
        };

        // Reset all top filter buttons - they're outside actions container
        $('.patient-top-filter-btn').each(function() {
            const $btn = $(this);
            $btn.removeClass('active-filter pl-3 pr-4 text-blue-800 text-purple-800 text-green-800')
                .addClass('px-3 bg-white text-gray-600 hover:bg-gray-50')
                .css('background', '');

            // Reset text span
            $btn.find('.patient-tab-text').removeClass('ml-1.5').addClass('opacity-0 w-0');

            // Reset icon color
            $btn.find('i').removeClass('text-blue-700 text-purple-700 text-green-700');
        });

        // Style the clicked button
        const gradient = colorGradients[color] || colorGradients['blue'];
        const textColor = textColors[color] || textColors['blue'];
        const iconColor = iconColors[color] || iconColors['blue'];

        $clickedButton.removeClass('px-3 bg-white text-gray-600 hover:bg-gray-50')
                     .addClass('active-filter pl-3 pr-4 ' + textColor)
                     .css('background', gradient);

        // Expand the text
        $clickedButton.find('.patient-tab-text').removeClass('opacity-0 w-0').addClass('ml-1.5');

        // Set icon color
        $clickedButton.find('i').addClass(iconColor);
    }

    // Handle type filter buttons (All, Task, Reminder, etc.) with expand/contract animation - PATIENT ACTIONS PAGE ONLY
    // Use more specific selector to avoid conflicts with sidebar
    $('#actions-container').on('click', '.patient-filter-btn', function(e) {
        e.preventDefault();
        console.log('=== PATIENT ACTIONS TYPE FILTER CLICKED ===');

        const clickedFilter = $(this).data('filter');
        const clickedColor = $(this).data('color');
        console.log('Type filter clicked:', clickedFilter, 'Color:', clickedColor);

        window.PatientActions.currentTypeFilter = clickedFilter;

        // Reset all filter buttons to default state - ONLY within actions container
        $('#actions-container .patient-filter-btn').removeClass('active-filter pl-3 pr-4')
                       .addClass('px-3 bg-white text-gray-600')
                       .css('background', '')
                       .removeClass(function(index, className) {
                           return (className.match(/(^|\s)text-\w+-800/g) || []).join(' ');
                       });

        // Reset all tab text to collapsed state - ONLY within actions container
        $('#actions-container .patient-filter-btn .patient-tab-text').addClass('opacity-0 w-0').removeClass('ml-1.5');

        // Apply active styling to clicked button
        $(this).removeClass('px-3 bg-white text-gray-600')
               .addClass('active-filter pl-3 pr-4')
               .addClass(`text-${clickedColor}-800`);

        // Apply gradient background based on color
        const gradients = {
            'indigo': 'linear-gradient(to right, #c7d2fe, #a5b4fc)',
            'blue': 'linear-gradient(to right, #dbeafe, #bfdbfe)',
            'amber': 'linear-gradient(to right, #fef3c7, #fde68a)',
            'red': 'linear-gradient(to right, #fecaca, #fca5a5)',
            'green': 'linear-gradient(to right, #d1fae5, #a7f3d0)',
            'purple': 'linear-gradient(to right, #e9d5ff, #d8b4fe)',
            'orange': 'linear-gradient(to right, #fed7aa, #fdba74)'
        };

        $(this).css('background', gradients[clickedColor] || gradients['indigo']);

        // Expand the tab text for the active button
        $(this).find('.patient-tab-text').removeClass('opacity-0 w-0').addClass('ml-1.5');

        console.log('Updated currentTypeFilter to:', window.PatientActions.currentTypeFilter);
        applyFilters();
    });

    // Handle search input
    $('#action-search-input').on('input', function() {
        console.log('Search input changed');
        applyFilters();
    });

});

// Reminder dropdown functionality is now in reminder_dropdown.js

// Function to get appropriate status text based on action type
function getCompletionStatusText(actionType) {
    console.log('getCompletionStatusText called with:', actionType, 'type:', typeof actionType);

    const statusMap = {
        'task': 'Completed',
        'reminder': 'Completed',
        'callback': 'Completed',
        'complaint': 'Resolved',
        'alert': 'Resolved'
    };

    const result = statusMap[actionType] || 'Completed';
    console.log('Status map lookup result:', result);
    console.log('Available keys in statusMap:', Object.keys(statusMap));

    return result;
}

// Function to apply filters to action rows
function applyFilters() {
    console.log('=== APPLYING FILTERS ===');
    console.log('Date filter:', window.PatientActions.currentDateFilter, 'Type filter:', window.PatientActions.currentTypeFilter);

    // Only apply filters if we have admin table rows (patient actions page)
    if (!$('.admin-table-row[data-action-id]').length) {
        console.log('No admin table rows found, skipping filter application');
        return;
    }

    const today = new Date().toISOString().split('T')[0];
    console.log('Today date:', today);
    let visibleCount = 0;
    let hiddenCount = 0;

    $('.admin-table-row[data-action-id]').each(function(index) {
        const row = $(this);
        const actionType = row.data('action-type');
        const actionDate = row.data('action-date');
        const actionCompleted = row.data('action-completed') === true || row.data('action-completed') === 'true';

        console.log(`Row ${index + 1}:`, {
            actionType,
            actionDate,
            actionCompleted,
            rawActionCompleted: row.data('action-completed'),
            currentDateFilter: window.PatientActions.currentDateFilter,
            currentTypeFilter: window.PatientActions.currentTypeFilter
        });

        let showRow = true;
        let reason = '';

        // Apply date filter (left side - Today/All Actions/Completed)
        if (window.PatientActions.currentDateFilter === 'today') {
            showRow = (actionDate === today) && !actionCompleted;
            reason = showRow ? 'matches today filter' : `doesn't match today filter (date: ${actionDate}, completed: ${actionCompleted})`;
        } else if (window.PatientActions.currentDateFilter === 'completed') {
            showRow = actionCompleted;
            reason = showRow ? 'matches completed filter' : `doesn't match completed filter (completed: ${actionCompleted})`;
        } else if (window.PatientActions.currentDateFilter === 'all') {
            // "All Actions" means all NON-completed actions (regardless of date)
            showRow = !actionCompleted;
            reason = showRow ? 'matches all actions filter (non-completed)' : `doesn't match all actions filter (action is completed)`;
        }

        // Apply type filter (right side - All/Task/Reminder/etc)
        if (showRow && window.PatientActions.currentTypeFilter !== 'all') {
            showRow = (actionType === window.PatientActions.currentTypeFilter);
            if (!showRow) reason = `doesn't match type filter (expected: ${window.PatientActions.currentTypeFilter}, actual: ${actionType})`;
        }

        console.log(`Row ${index + 1} result:`, { showRow, reason });

        // Show or hide the row
        if (showRow) {
            row.show();
            visibleCount++;
        } else {
            row.hide();
            hiddenCount++;
        }
    });

    console.log('=== FILTER RESULTS ===');
    console.log('Visible actions:', visibleCount, 'Hidden actions:', hiddenCount);
    console.log('========================');
}

// Function to initialize action rows
function initializeActionRows() {
  console.log('Initializing action rows...');
  
  // Count how many action rows we have
  var actionRowCount = $('tr[data-action-id]').not('.action-expanded-row, .comments-section').length;
  console.log('Found', actionRowCount, 'action rows');
  
  var expandedRowCount = $('.action-expanded-row').length;
  console.log('Found', expandedRowCount, 'expandable rows');
  
  // Hide all expanded rows initially
  $('.action-expanded-row').hide();
  $('.comments-section').hide();
  console.log('All expanded rows and comments sections hidden initially');
  
  // Don't show all actions - let our filtering system handle the initial display
  console.log('Skipping showAllActions() - filtering system will handle initial display');
  
  // Log all action IDs for debugging
  $('tr[data-action-id]').not('.action-expanded-row, .comments-section').each(function() {
    console.log('Action row with action-id:', $(this).data('action-id'));
  });
}

// Function to show all actions initially
function showAllActions() {
  console.log('=== SHOWING ALL ACTIONS (from actions.js) ===');

  // Hide all expanded rows and comments sections initially
  $('.action-expanded-row').hide();
  $('.comments-section').hide();

  // Show all main action rows
  $('tr[data-action-id]').each(function() {
    var $row = $(this);

    // Skip expanded rows and comments sections
    if ($row.hasClass('action-expanded-row') || $row.hasClass('comments-section')) {
      return true; // continue to next iteration
    }

    // Show all action rows - let the filter handle what to show/hide
    $row.show().removeClass('hidden');
    console.log('Showing action row:', $row.data('action-id'));
  });

  console.log('Showed all actions from actions.js');
}

// Function to hide completed actions
function hideCompletedActions() {
  // First, hide all expanded rows and comments sections
  $('.action-expanded-row').hide();
  $('.comments-section').hide();
  
  // Then process each main action row
  $('tr[data-action-id]').each(function() {
    var $row = $(this);
    var actionId = $row.data('action-id');
    
    // Skip expanded rows and comments sections (we already hid them)
    if ($row.hasClass('action-expanded-row') || $row.hasClass('comments-section')) {
      return true; // continue to next iteration
    }
    
    // Check if this is a completed action
    if ($row.find('.bg-emerald-100').length > 0 || $row.hasClass('opacity-75')) {
      // Hide this row and its related rows
      $row.hide();
      $('.action-expanded-row[data-action-id="' + actionId + '"]').hide();
      $('.comments-section[data-action-id="' + actionId + '"]').hide();
    }
  });
  
  // Update the completed button to show it's not active
  $('#toggle-completed-btn').removeClass('bg-emerald-100 text-emerald-800 border-emerald-200')
                           .addClass('text-emerald-600 border-emerald-200 bg-emerald-50/80 hover:bg-emerald-100/80');
}

// Function to show completed actions
function showCompletedActions() {
  // Show all main action rows
  $('tr[data-action-id]').each(function() {
    var $row = $(this);
    
    // Skip expanded rows and comments sections (keep them hidden unless explicitly toggled)
    if (!$row.hasClass('action-expanded-row') && !$row.hasClass('comments-section')) {
      $row.show();
    }
  });
  
  // Update the completed button to show it's active
  $('#toggle-completed-btn').removeClass('text-emerald-600 border-emerald-200 bg-emerald-50/80 hover:bg-emerald-100/80')
                           .addClass('bg-emerald-100 text-emerald-800 border-emerald-200');
}

// Initialize on document ready and also when Turbolinks loads a new page
$(document).on('turbolinks:load', function() {
  console.log('turbolinks:load event fired');
  initializeActionRows();
});

$(document).on('page:change', function() {
  console.log('page:change event fired');
  initializeActionRows();
});

$(document).ready(function() {
  console.log('document.ready event fired');
  initializeActionRows();

  // Reminder dropdown functionality is now in reminder_dropdown.js
  console.log('Reminder dropdown handled by reminder_dropdown.js');

  // Modal opening functionality - now using unified modal manager
  function openNewActionModal() {
    if (window.unifiedActionModal) {
      window.unifiedActionModal.openModal('patientNewActionModal');
    } else {
      console.error('Unified action modal manager not available');
    }
  }

  function closeNewActionModal() {
    if (window.unifiedActionModal) {
      window.unifiedActionModal.closeModal('patientNewActionModal');
    } else {
      console.error('Unified action modal manager not available');
    }
  }

  // Handle edit action button clicks
  $(document).on('click', '.edit-action-btn', function(e) {
    e.preventDefault();
    const actionId = $(this).data('action-id');

    if (!actionId) {
      console.error('No action ID found for edit button');
      return;
    }

    // Fetch action data via AJAX
    $.ajax({
      url: `/admin/actions/${actionId}/edit`,
      method: 'GET',
      dataType: 'json',
      success: function(response) {
        if (response.success && response.action) {
          // Open modal in edit mode with action data
          if (window.unifiedActionModal) {
            window.unifiedActionModal.openModal('patientNewActionModal', {
              editAction: response.action
            });
          } else {
            console.error('Unified action modal manager not available');
          }
        } else {
          console.error('Failed to fetch action data:', response);
          alert('Failed to load action data for editing');
        }
      },
      error: function(xhr, status, error) {
        console.error('Error fetching action data:', error);
        alert('Failed to load action data for editing');
      }
    });
  });

  // Modal event handlers are now managed by the unified modal manager
  // The unified modal manager automatically handles:
  // - #open-new-action-modal click events
  // - .modal-close and .modal-cancel click events
  // - overlay click events
  
  // Toggle completed actions visibility when the Completed button is clicked
  $(document).on('click', '#toggle-completed-btn', function(e) {
    e.preventDefault();
    console.log('Completed button clicked');
    
    // Check if any completed actions are currently hidden
    var anyHidden = false;
    $('tr[data-action-id]').each(function() {
      var $row = $(this);
      if (($row.find('.bg-emerald-100').length > 0 || $row.hasClass('opacity-75')) && $row.is(':hidden')) {
        anyHidden = true;
        return false; // Break the loop
      }
    });
    
    // Toggle visibility based on current state
    if (anyHidden) {
      console.log('Showing completed actions');
      showCompletedActions();
    } else {
      console.log('Hiding completed actions');
      hideCompletedActions();
    }
  });
  
  // Toggle on row click (except for buttons)
  $(document).on('click', 'tr[data-action-id]', function(e) {
    // Don't trigger if clicking on a button, input, or link
    if ($(e.target).closest('button, input, a, .complete-action-btn, .reminder-btn').length > 0) {
      return;
    }
    
    // Don't trigger for expanded rows or comments sections
    if ($(this).hasClass('action-expanded-row') || $(this).hasClass('comments-section')) {
      return;
    }
    
    console.log('Action row clicked!');
    var actionId = $(this).data('action-id');
    console.log('Action ID:', actionId);
    
    // Toggle the expanded row for this action
    var $expandedRow = $('.action-expanded-row[data-action-id="' + actionId + '"]');
    
    // If this row is already expanded, just hide it
    if ($expandedRow.is(':visible')) {
      $expandedRow.hide();
    } else {
      // Otherwise hide all expanded rows first, then show this one
      $('.action-expanded-row').hide();
      $expandedRow.show();
    }
  });
  
  // NOTE: Comments functionality is now handled by the new action_comments.js system
  // This old jQuery system is disabled to prevent conflicts
  // $(document).on('click', '.comments-toggle', function(e) {
  //   e.preventDefault();
  //   e.stopPropagation();
  //
  //   var actionId = $(this).data('action-id');
  //   console.log('Comments button clicked for action-id:', actionId);
  //
  //   // Get the comments section for this action
  //   var commentsSection = $('.comments-section[data-action-id="' + actionId + '"]');
  //
  //   // Hide all other comments sections first
  //   $('.comments-section').not(commentsSection).hide();
  //
  //   // Toggle this comments section
  //   commentsSection.toggle();
  //   console.log('Toggled comments section, now visible:', commentsSection.is(':visible'));
  // });
  
  // Handle Complete button click
  $(document).on('click', '.complete-action-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    var $button = $(this);
    var actionId = $button.data('action-id');
    console.log('Complete button clicked for action-id:', actionId);

    // Disable the button to prevent double-clicks
    $button.prop('disabled', true).addClass('opacity-50');

    // Show loading state
    var originalText = $button.html();
    $button.html('<i class="fas fa-spinner fa-spin h-3.5 w-3.5 mr-1"></i>Saving...');

    // Make AJAX request to mark as completed
    $.ajax({
      url: '/admin/actions/' + actionId + '/mark_as_completed',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          // Reload the page to refresh the action buttons and status
          window.location.reload();
        } else {
          console.error('Failed to complete action:', response.error);
          alert('Failed to complete action. Please try again.');
          $button.prop('disabled', false).removeClass('opacity-50').html(originalText);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error completing action:', error);
        alert('An error occurred while completing the action. Please try again.');
        $button.prop('disabled', false).removeClass('opacity-50').html(originalText);
      }
    });
  });

  // Handle Reactivate button click
  $(document).on('click', '.reactivate-action-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    var $button = $(this);
    var actionId = $button.data('action-id');
    console.log('Reactivate button clicked for action-id:', actionId);

    // Disable the button to prevent double-clicks
    $button.prop('disabled', true).addClass('opacity-50');

    // Show loading state
    var originalText = $button.html();
    $button.html('<i class="fas fa-spinner fa-spin h-3.5 w-3.5 mr-1"></i>Reactivating...');

    // Make AJAX request to reactivate (mark as uncompleted)
    $.ajax({
      url: '/admin/actions/' + actionId + '/mark_as_completed',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          // Reload the page to refresh the action buttons and status
          window.location.reload();
        } else {
          console.error('Failed to reactivate action:', response.error);
          alert('Failed to reactivate action. Please try again.');
          $button.prop('disabled', false).removeClass('opacity-50').html(originalText);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error reactivating action:', error);
        alert('An error occurred while reactivating the action. Please try again.');
        $button.prop('disabled', false).removeClass('opacity-50').html(originalText);
      }
    });
  });
  
  // Helper function to show toast notifications
  function showToast(message, type = 'info') {
    var bgColor, textColor, borderColor, icon;
    
    switch(type) {
      case 'success':
        bgColor = 'bg-emerald-50';
        textColor = 'text-emerald-800';
        borderColor = 'border-emerald-200';
        icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-emerald-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>';
        break;
      case 'error':
        bgColor = 'bg-red-50';
        textColor = 'text-red-800';
        borderColor = 'border-red-200';
        icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>';
        break;
      default:
        bgColor = 'bg-blue-50';
        textColor = 'text-blue-800';
        borderColor = 'border-blue-200';
        icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>';
    }
    
    var $toast = $('<div>', {
      class: `fixed bottom-4 right-4 ${bgColor} border ${borderColor} ${textColor} px-4 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2 animate-in slide-in-from-bottom-5`
    }).html(`${icon}<span>${message}</span>`);
    
    $('body').append($toast);
    
    // Auto-remove after 3 seconds
    setTimeout(function() {
      $toast.addClass('animate-out fade-out');
      setTimeout(function() {
        $toast.remove();
      }, 300);
    }, 3000);
  }
});
