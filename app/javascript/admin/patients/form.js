$(document).ready(function () {
  let $patientForm = $("#patientInformationForm");
  if (!$patientForm.length) return; // Bail out if the patient form is not present

  $(".new-password-btn").click(function() {
    Swal.fire({
      title: 'Set new password?',
      text: "The patient will not be able to log in with their current password.",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Set new password',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        $("#newPasswordModal").modal("show");
      }
    });
  })

  const newPasswordModal = document.querySelector("#newPasswordModal");
  if(newPasswordModal) {
    newPasswordModal.addEventListener("show.bs.modal", function (event) {
      fetch(`/admin/patients/${newPasswordModal.dataset.id}/reset_password`, {
        method: "POST",
        headers: {
          "X-CSRF-Token": document.querySelector("meta[name=csrf-token]").content,
          "Content-Type": "application/json"
        }
      }).then(function (response) {
        if (response.ok) {
          return response.json();
        }
        throw new Error("Network response was not ok.");
      }).then(function (data) {
        newPasswordModal.querySelector("#new_password").value = data.password;
      });
    });
  }

  $(".detail-box").on("click", function () {
    $("#detailsFooter").addClass("show");
    setTimeout(function () {
      $("#detailsFooter").addClass("play-anim");
    }, 100);
  });


  $(".detail-box").on("input change", function() {
    let title     = $("[name='patient[title]']").val()       || "";
    let firstName = $("[name='patient[first_name]']").val()   || "";
    let lastName  = $("[name='patient[last_name]']").val()    || "";
    let pronouns  = $("[name='patient[pronouns]']").val()     || "";

    let fullName = [title, firstName, lastName].filter(Boolean).join(" ");

    $("#patient_full_name").text(fullName);
    $("#patient_pronouns").text(pronouns);
  });

  // Check if it's a new user
  if ($patientForm.data("skip-practice-select") === true) return;

  // If more than one practice is available, prompt user to select
  if (window._availablePractices.length > 1) {
    let inputOptions = {};

    window._availablePractices.forEach(function (practice) {
      inputOptions[practice.id] = practice.name;
    });

    Swal.fire({
      title: 'Select a Practice',
      input: 'select',
      inputOptions: inputOptions,
      inputPlaceholder: 'Select a practice',
      showCancelButton: false,
      allowOutsideClick: false,
      allowEscapeKey: false,
      inputValidator: function (value) {
        return new Promise(function (resolve) {
          if (value) {
            resolve();
          } else {
            resolve('You need to select a practice');
          }
        });
      }
    }).then(function (result) {
      if (result.isConfirmed) {
        const chosenPracticeId = result.value;

        // For new patients, use the create_with_practice endpoint
        if ($patientForm.attr('action').includes('/admin/patients') && !$patientForm.attr('action').includes('/admin/patients/')) {
          createPatientWithPractice(chosenPracticeId);
        } else {
          // For existing patients, use the normal form submission
          $("#patient_practice_ids")
            .val([chosenPracticeId])
            .trigger("change");
          $patientForm.submit();
        }
      }
    });
  } else {
    // Single practice scenario
    const practiceId = window._availablePractices[0].id;

    // For new patients, use the create_with_practice endpoint
    if ($patientForm.attr('action').includes('/admin/patients') && !$patientForm.attr('action').includes('/admin/patients/')) {
      createPatientWithPractice(practiceId);
    } else {
      // For existing patients, use the normal form submission
      $("#patient_practice_ids")
        .val([practiceId])
        .trigger("change");
      $patientForm.submit();
    }
  }

  function createPatientWithPractice(practiceId) {
    // Serialize the form data
    const formData = new FormData($patientForm[0]);
    formData.append('practice_id', practiceId);
    formData.append('saved', 'true');

    // Show loading state
    const submitBtn = $patientForm.find('button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('Creating...');

    $.ajax({
      url: '/admin/patients/create_with_practice',
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content'),
        'Accept': 'application/json'
      },
      success: function(response) {
        if (response.success && response.redirect_url) {
          window.location.href = response.redirect_url;
        } else {
          // Handle error
          alert(response.errors ? response.errors.join(', ') : 'Failed to create patient');
          submitBtn.prop('disabled', false).text(originalText);
        }
      },
      error: function(xhr) {
        let errorMessage = 'An error occurred while creating the patient';
        try {
          const errorResponse = JSON.parse(xhr.responseText);
          if (errorResponse.errors && errorResponse.errors.length > 0) {
            errorMessage = errorResponse.errors.join(', ');
          }
        } catch (e) {
          // If we can't parse JSON, use default message
        }
        alert(errorMessage);
        submitBtn.prop('disabled', false).text(originalText);
      }
    });
  }
});
