<!DOCTYPE html>
<html lang="en">
<head>
  <title><%= @subject %></title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f3f4f6;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    .email-container {
      max-width: 672px;
      margin: 32px auto;
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    .email-header {
      padding: 24px 32px;
      margin-bottom: 24px;
    }
    .header-table {
      width: 100%;
      border-collapse: collapse;
    }
    .logo-cell {
      text-align: left;
      vertical-align: middle;
    }
    .button-cell {
      text-align: right;
      vertical-align: top;
      width: auto;
      padding-top: 0;
      white-space: nowrap;
    }
    .logo {
      height: 88px;
      object-fit: contain;
    }
    .portal-button {
      position: relative;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      border-radius: 4px;
      background-color: #f3f4f6;
      padding: 6px 10px;
      font-size: 11px;
      font-weight: 500;
      color: #374151 !important;
      text-decoration: none;
      white-space: nowrap;
      min-width: 120px;
    }
    .portal-button:hover {
      background-color: #e5e7eb;
      color: #374151 !important;
    }
    .divider {
      border: 0;
      border-top: 1px solid #e5e7eb;
    }
    .email-content {
      padding: 0 24px 0 32px;
      margin: 32px 0;
    }
    .content-header {
      background-color: #f8fafc;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .content-title {
      font-size: 14px;
      font-weight: 600;
      color: #111827;
      text-align: center;
      margin: 0;
    }
    .content-body {
      text-align: left;
    }
    .content-text {
      font-size: 14px;
      color: #374151;
      margin-bottom: 32px;
      line-height: 1.5;
    }
    .manage-link {
      font-size: 12px;
      color: #2563eb;
      text-decoration: none;
      margin-bottom: 24px;
    }
    .manage-link:hover {
      text-decoration: underline;
    }
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin: 24px 0;
    }
    .button {
      display: inline-block;
      border-radius: 9999px;
      padding: 8px 20px;
      font-size: 12px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
    }
    .button-secondary {
      background-color: #e5e7eb;
      color: #374151;
    }
    .button-secondary:hover {
      background-color: #d1d5db;
    }
    .button-primary {
      background-color: #dbeafe;
      color: #1e40af;
    }
    .button-primary:hover {
      background-color: #bfdbfe;
    }
    .email-footer {
      margin-top: 24px;
      text-align: center;
      font-size: 12px;
      color: #6b7280;
      padding: 0 24px 24px 32px;
    }
    .footer-text {
      margin: 4px 0;
    }
    .footer-contact {
      padding-top: 8px;
    }
    .icon {
      width: 10px;
      height: 10px;
    }

    .manage-link {
      font-size: 12px;
      color: #2563eb;
      text-decoration: none;
      margin-bottom: 24px;
    }
    .buttons-container {
      text-align: center;
      margin: 24px 0;
      width: 100%;
    }
    .buttons-container a {
      display: inline-block;
      margin: 0 8px;
    }
    .email-button {
      display: inline-block;
      padding: 8px 20px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      min-width: 100px;
      box-sizing: border-box;
    }
    .email-button-accept {
      background-color: #dbeafe;
      color: #1e40af;
      border: 1px solid #bfdbfe;
    }
    .email-button-accept:hover {
      background-color: #bfdbfe;
      color: #1e40af;
    }
    .email-button-decline {
      background-color: #e5e7eb;
      color: #374151;
      border: 1px solid #d1d5db;
    }
    .email-button-decline:hover {
      background-color: #d1d5db;
      color: #374151;
    }
    .email-button-default {
      background-color: #e5e7eb;
      color: #374151;
      border: 1px solid #d1d5db;
    }
    .email-button-default:hover {
      background-color: #d1d5db;
      color: #374151;
    }

    /* Mobile responsiveness */
    @media (max-width: 640px) {
      .email-container {
        margin: 16px;
        border-radius: 8px;
      }
      .email-header {
        padding: 16px;
        flex-direction: column;
        gap: 16px;
      }
      .email-content {
        padding: 0 16px;
      }
      .email-footer {
        padding: 0 16px 16px;
      }
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <table class="header-table">
        <tr>
          <td class="logo-cell">
            <img alt="UPOD Medical Logo" class="logo" src="<%= @patient&.current_practice&.logo&.url %>">
          </td>
          <td class="button-cell">
            <a href="<%= ENV['HOST'] %>/patients/patients/dashboard" class="portal-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                <polyline points="10 17 15 12 10 7"></polyline>
                <line x1="15" x2="3" y1="12" y2="12"></line>
              </svg>
              Login to Patient Portal
            </a>
          </td>
        </tr>
      </table>
    </div>

    <hr class="divider">

    <div class="email-content">
      <%= yield %>
    </div>

    <hr class="divider">

    <div class="email-footer">
      <p class="footer-text">You are receiving this email as a registered patient of UPOD Medical.</p>
      <p class="footer-text"> UPOD Medical, all rights reserved.</p>
      <div class="footer-contact">
        <p class="footer-text"><%= @patient.current_practice.full_address %></p>
        <p class="footer-text">Contact: <%= @patient.current_practice.phone %></p>
      </div>
    </div>
  </div>
</body>
</html>
