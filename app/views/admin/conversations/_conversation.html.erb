<div class="flex-1 flex flex-col bg-white rounded-l-xl shadow-sm overflow-hidden">
  <div class="flex flex-col h-full">
    <%
      newest_message = @messages.first if @messages.present?
      auto_select_type = 'internal'
      auto_select_account_id = nil

      if newest_message
        case newest_message.message_type
        when 'internal'
          auto_select_type = 'internal'
        when 'outbound', 'inbound'
          account_identifier = newest_message.message_type == 'outbound' ? newest_message.from : newest_message.to

          if account_identifier.present?
            matching_account = current_user.communication_accounts.find_by(identifier: account_identifier)
            if matching_account
              if ['sms', 'whatsapp'].include?(matching_account.account_type)
                auto_select_type = 'messaging'
                auto_select_account_id = matching_account.id
              elsif matching_account.account_type == 'email'
                auto_select_type = 'email'
                auto_select_account_id = matching_account.id
              end
            end
          end
        end
      end
    %>

    <div class="flex items-center justify-between p-4 border-b border-gray-100">
      <div class="flex items-center space-x-3">
        <% if conversation.patient %>
          <span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 bg-gray-200">
            <%= render 'layouts/shared/user_avatar', user: conversation.patient, width: 32, height: 32 %>
          </span>
        <% else %>
          <% recipients = conversation.internal_recipients.first(10) %>
          <% recipients.each do |recipient| %>
            <span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 bg-gray-200">
              <%= render 'layouts/shared/user_avatar', user: recipient, width: 32, height: 32 %>
            </span>
          <% end %>
        <% end %>
        <% if conversation.patient %>
          <h2 class="text-base font-medium"><%= link_to conversation_participants_display(conversation), admin_patient_path(conversation.patient) %></h2>
        <% else %>
          <h2 class="text-base font-medium"><%= conversation.name.presence || conversation_participants_display(conversation) %></h2>
        <% end %>
      </div>
      <div class="flex items-center space-x-2">
        <% unless conversation.internal? %>
        <!-- Message type filter toggle buttons -->
        <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
          <!-- All messages button -->
          <button
            type="button"
            id="filter-all-btn"
            data-filter-type="all"
            class="filter-button active-filter bg-gradient-to-r from-gray-200 to-gray-300 text-gray-800 min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layers h-4 w-4 text-gray-700">
              <path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"/>
              <path d="m22 12-8.6 3.91a2 2 0 0 1-1.66 0L3.1 12"/>
              <path d="m22 17-8.6 3.91a2 2 0 0 1-1.66 0L3.1 17"/>
            </svg>
            <span class="filter-text text-xs font-medium tracking-wide ml-2">All</span>
          </button>

          <!-- Internal notes button -->
          <button
            type="button"
            id="filter-internal-btn"
            data-filter-type="internal"
            class="filter-button bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
              <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
              <path d="M10 9H8"></path>
              <path d="M16 13H8"></path>
              <path d="M16 17H8"></path>
            </svg>
            <span class="filter-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Internal</span>
          </button>

          <!-- SMS/WhatsApp button -->
          <button
            type="button"
            id="filter-messaging-btn"
            data-filter-type="messaging"
            class="filter-button bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
            <span class="filter-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">SMS/WhatsApp</span>
          </button>

          <!-- Email button -->
          <button
            type="button"
            id="filter-email-btn"
            data-filter-type="email"
            class="filter-button bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4">
              <rect width="20" height="16" x="2" y="4" rx="2"></rect>
              <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
            </svg>
            <span class="filter-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Email</span>
          </button>
        </div>
        <% end %>

        <!-- Search input -->
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
          <input id="conversation-message-search" class="ml-6 flex border-input px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-0 rounded-full text-[14px]" placeholder="Search..." type="search">
        </div>
      </div>
    </div>

    <div id="sender-notification" class="bg-amber-100 rounded-lg p-3 text-sm transition-colors duration-300">
      <div class="flex items-center justify-center space-x-2">
        <div id="sender-indicator">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4 text-amber-700">
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
            <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
            <path d="M10 9H8"></path>
            <path d="M16 13H8"></path>
            <path d="M16 17H8"></path>
          </svg>
        </div>
        <span class="font-medium" id="message-type-text">You are sending an internal note</span>
      </div>
    </div>

    <div class="flex-1 overflow-y-auto p-4" id="chat-container">
      <!-- Message pagination loader (shown at top when loading older messages) -->
      <div id="message-pagination-loader" class="text-center py-3 hidden">
        <div class="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-blue-500" role="status">
          <span class="sr-only">Loading older messages...</span>
        </div>
        <div class="text-xs text-gray-500 mt-1">Loading older messages...</div>
      </div>

      <div id="chat-messages" class="space-y-6 flex flex-col-reverse"
           data-conversation-id="<%= conversation.id %>"
           data-internal="<%= conversation.internal? ? "1" : "0" %>"
           data-current-page="<%= @current_page || 1 %>"
           data-has-more="<%= @has_more_messages ? 'true' : 'false' %>"
           <% if controller.controller_name == "lab_works" %> data-lab-work-id="<%= @lab_work.id %>"<% end %>>
        <%= render partial: 'admin/conversations/messages', locals: { messages: messages } %>
      </div>
    </div>

    <div class="border-t border-gray-100 relative">
      <div class="p-3">

        <%= form_with(url: create_message_admin_conversation_path(conversation), method: :post, id: 'message-form', class: 'flex flex-col') do |f| %>
          <%= f.hidden_field :message_type, value: auto_select_type %>
          <%= f.hidden_field :account_id, value: auto_select_account_id if auto_select_account_id %>
          <style>
            /* Always hide the textarea since TinyMCE replaces it */
            #message-editor {
              display: none !important;
              visibility: hidden !important;
            }

            /* Hide TinyMCE only when template is selected */
            #message-form.template-selected .tox-tinymce {
              display: none !important;
              visibility: hidden !important;
              height: 0 !important;
              overflow: hidden !important;
            }
          </style>
          <%= f.text_area :content, id: 'message-editor', class: 'tinymce', placeholder: 'Type your message here...' %>

          <!-- WhatsApp Templates Selection Block (initially hidden) -->
          <div id="whatsapp-templates-block" class="hidden mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <span class="material-symbols-outlined text-green-600 text-sm">chat</span>
                <h3 class="text-sm font-medium text-green-800">WhatsApp Templates</h3>
              </div>
              <button type="button" id="deselect-template-btn" class="text-xs text-green-600 hover:text-green-800 underline">Use custom message instead</button>
            </div>

            <div class="mb-3">
              <label for="whatsapp-template-select" class="block text-xs font-medium text-green-700 mb-1">Select a template:</label>
              <select id="whatsapp-template-select" class="w-full px-3 py-2 text-sm border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Choose a WhatsApp template...</option>
                <% @whatsapp_templates.includes(:whatsapp_components).each do |template| %>
                  <option value="<%= template.id %>"
                          data-content="<%= html_escape(template.to_html) %>"
                          data-preview="<%= html_escape(template.whatsapp_components.map(&:text).reject(&:blank?).join('<br/>')) %>">
                    <%= template.name %>
                  </option>
                <% end %>
              </select>
            </div>

            <!-- Template Preview -->
            <div id="template-preview" class="hidden mt-3 p-3 bg-white border border-green-200 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <span class="material-symbols-outlined text-green-600 text-sm">visibility</span>
                <span class="text-xs font-medium text-green-700">Template Preview:</span>
              </div>
              <div id="template-preview-content" class="text-sm text-gray-800 bg-gray-50 p-3 rounded border">
                <!-- Template content will be populated here -->
              </div>
            </div>
          </div>
        <% end %>

        <div class="flex items-center justify-between mt-3">
              <!-- Message type selector -->
      <div class="px-3 mb-4">
        <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
          <!-- Internal Note button -->
          <button type="button" id="internal-note-btn" data-type="internal" class="message-type-button <%= auto_select_type == 'internal' ? 'active-type' : '' %> <%= auto_select_type == 'internal' ? 'bg-gradient-to-r from-amber-200 to-amber-300 text-amber-800' : 'bg-white text-gray-600 hover:bg-gray-50' %> min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4 text-amber-700">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
              <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
              <path d="M10 9H8"></path>
              <path d="M16 13H8"></path>
              <path d="M16 17H8"></path>
            </svg>
            <span class="button-text text-xs font-medium tracking-wide">Internal Note</span>
          </button>

          <!-- SMS/WhatsApp buttons - one for each account -->
          <% unless conversation.internal? %>
            <% current_user.communication_accounts.where(account_type: ["sms", "whatsapp"]).each do |account| %>
              <button
                type="button"
                id="messaging-btn-<%= account.id %>"
                data-type="messaging"
                data-account-id="<%= account.id %>"
                data-account-type="<%= account.account_type %>"
                data-account-name="<%= account.display_name %>"
                class="message-type-button <%= auto_select_type == 'messaging' && auto_select_account_id == account.id ? 'active-type' : '' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden"
              >
              <% if account.account_type == "whatsapp" %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle h-4 w-4">
                  <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                </svg>
              <% else %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              <% end %>
              <span class="button-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300"><%= account.display_name %></span>
            </button>
          <% end %>
          <% end %>


          <!-- Email buttons - one for each account -->
          <% unless conversation.internal? %>
            <% current_user.communication_accounts.where(account_type: "email").each do |account| %>
              <a
                href="javascript:void(0)"
                id="email-account-<%= account.id %>"
                data-type="email"
                data-account-id="<%= account.id %>"
                data-account-name="<%= account.display_name %>"
                data-practice-id="<%= account.practice_id %>"
                class="message-type-button <%= auto_select_type == 'email' && auto_select_account_id == account.id ? 'active-type' : '' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden cursor-pointer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4">
                  <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                </svg>
                <span class="button-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300"><%= account.display_name %></span>
              </a>
            <% end %>
          <% end %>
        </div>
      </div>
          <div class="flex items-center space-x-2">
            <!-- Template Selection Button (only visible for SMS/Email) -->
            <button
              type="button"
              id="template-selector-btn"
              class="hidden h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden bg-white text-gray-600 hover:bg-gray-50"
              title="Select template"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                <path d="M10 9H8"></path>
                <path d="M16 13H8"></path>
                <path d="M16 17H8"></path>
              </svg>
              <span class="button-text text-xs font-medium tracking-wide transition-all duration-300 ml-1">Template</span>
            </button>

            <button type="button" id="send-message-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 py-2 bg-amber-400 text-white rounded-full px-4">Save Internal Note</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Template Selection Modal -->
  <div id="template-modal" class="fixed inset-0 flex items-center justify-center z-50 hidden" style="background-color:rgba(0, 0, 0, 0.5);">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col">
      <div class="flex items-center justify-between p-4 border-b">
        <h3 class="text-lg font-semibold">Select Message Template</h3>
        <button type="button" id="close-template-modal" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Search Input -->
      <div class="p-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            type="text"
            id="template-search"
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            placeholder="Search templates..."
          />
        </div>
      </div>

      <!-- Template Content -->
      <div class="flex-1 overflow-hidden p-4">
        <div id="template-loading" class="text-center py-8">
          <div class="inline-block h-8 w-8 animate-spin rounded-full border-2 border-solid border-current border-r-transparent text-blue-500"></div>
          <p class="mt-2 text-gray-600">Loading templates...</p>
        </div>
        <div id="template-list" class="hidden h-96 overflow-y-auto pr-2 space-y-3">
          <!-- Templates will be loaded here -->
        </div>
        <div id="template-empty" class="hidden text-center py-8 text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="mt-2">No templates available for this communication type.</p>
        </div>
        <div id="template-no-results" class="hidden text-center py-8 text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="mt-2">No templates match your search.</p>
        </div>
      </div>
    </div>
  </div>
</div>
