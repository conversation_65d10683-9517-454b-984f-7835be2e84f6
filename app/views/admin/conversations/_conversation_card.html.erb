<div class="conversation-card inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground w-full h-16 rounded-lg justify-start px-4 py-3 text-left space-x-3 hover:bg-gray-50 transition-all duration-200 shadow-sm border cursor-pointer <%= highlight ? 'bg-gray-50 border-gray-200/70' : 'border-transparent bg-transparent' %>" data-conversation-id="<%= conversation.id %>">
  <div class="relative">
    <div class="relative">
      <% if conversation.patient.present? %>
        <div class="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center">
          <span class="text-blue-600 font-medium text-sm"><%= conversation.patient.first_name[0] %><%= conversation.patient.last_name[0] %></span>
        </div>
      <% else %>
        <div class="h-9 w-9 rounded-full bg-gray-100 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4">
            <rect width="20" height="16" x="2" y="4" rx="2"></rect>
            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
          </svg>
        </div>
      <% end %>
      <% if highlight || conversation_has_unread_inbound?(conversation) %>
        <div class="absolute top-0 right-0 h-2.5 w-2.5 rounded-full bg-blue-500 border-2 border-white"></div>
      <% end %>
    </div>
  </div>
  <div class="flex-1 min-w-0">
    <div class="text-sm font-medium text-gray-800 truncate"><%= conversation.name.presence || conversation_participants_display(conversation) %></div>
    <div class="text-xs text-gray-500 truncate"><%= sanitize conversation_last_message_content(conversation) %></div>
  </div>
  <div class="flex items-center relative">
    <% if highlight || conversation_has_unread_inbound?(conversation) %>
      <div class="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-full absolute -right-3 -top-5">New message</div>
    <% elsif conversation_last_message_date(conversation) %>
      <div class="text-xs text-gray-400"><%= conversation_last_message_date(conversation) %></div>
    <% end %>
  </div>
</div>
