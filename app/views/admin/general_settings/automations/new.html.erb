<main class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area min-h-[calc(100vh-56px)] bg-gray-50 automations-page">
  <%= render 'admin/general_settings/side_panel' %>

  <div id="templates-json" data-templates="<%= @templates.to_json %>"></div>

  <div class="flex-grow">
    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-slate-800">New Automation</h1>
          <p class="text-sm text-slate-600 mt-1">Create a new automation to streamline your practice workflows</p>
        </div>
        <%= link_to admin_general_settings_automations_path, class: "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-white hover:bg-gray-50 text-slate-700 border border-slate-200 shadow-sm font-medium px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md" do %>
          <span class="material-symbols-outlined">arrow_back</span>
          Back to Automations
        <% end %>
      </div>

      <div id="automation-form"
           data-automation-triggers='<%= AUTOMATION_TRIGGERS.sort_by { |trigger| trigger[:key] }.to_json %>'
           data-automation-conditions='<%= @resolved_conditions.to_json %>'
           data-automation-actions='<%= @resolved_actions.to_json %>'>
        <%= render 'form', automation: @automation %>
      </div>
    </div>
  </div>
</main>
