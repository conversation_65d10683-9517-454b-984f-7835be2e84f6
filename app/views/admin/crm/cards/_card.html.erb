<%#= render 'admin/crm/shared/card_labels', labels: card.all_labels, compact: true %>

  <div tabindex="0" class="crm-patient-card cursor-grab bg-white rounded-xl p-4 pb-3 mb-3 relative <%=
    if card.new_card?
      'border-2 border-red-500'    # HL takes priority
    elsif card.has_unread_messages?
      'border-2 border-yellow-500' # Only unread message
    else
      'border border-gray-200'     # Default border
    end
  %> shadow-lg hover:shadow-xl"
    data-card-id="<%= card.id %>"
    data-patient-id="<%= card.patient_id %>"
    data-last-contacted="<%= card.last_contacted_at&.iso8601 %>"
    data-card-text="<%= card.patient&.full_name %> <%= card.title %> <%= card.description %>">
    <div class="flex justify-between items-start">
      <div class="flex-1">
        <% if card.new_card? || card.has_unread_messages? %>
        <div class="mb-2">
          <div class="inline-flex items-center px-2 py-1 rounded-full 
            <%= if card.new_card?
                  'bg-gradient-to-r from-red-100 to-red-100 border border-red-500'
                else
                  'bg-yellow-100 border border-yellow-200'
                end %>">
            
            <% if card.new_card? %>
            <div class="flex items-center">
              <span class="inline-block w-2 h-2 rounded-full bg-red-400 animate-pulse mr-1.5"></span>
              <span class="text-[12px] font-medium text-red-600">HL</span>
            </div>
            <% end %>
            
            <% if card.new_card? && card.has_unread_messages? %>
            <div class="h-3 w-px bg-red-200 mx-1.5"></div>
            <% end %>
            
            <% if card.has_unread_messages? %>
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-3 h-3 text-yellow-600 mr-1.5">
                <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
              </svg>
              <span class="text-[12px] font-medium text-yellow-700">New Message</span>
            </div>
            <% end %>
          </div>
        </div>
        <% end %>
        <div class="text-[15px] font-medium flex items-center">
          <% if card.patient.present? %>
            <%= link_to card.patient.full_name, admin_crm_card_path(card), class: "patient-name hover:text-blue-600 cursor-pointer", data: { no_drag: true } %>
          <% else %>
            <span class="patient-name"><%= card.title %></span>
          <% end %>
          <% if card.description.present? %>
            <span class="inline-flex ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-3.5 h-3.5 text-blue-500 note-icon" data-card-id="<%= card.id %>" data-description="<%= card.description.to_s.gsub(/<\/?.+?>/, '').gsub(/\r|\n/, ' ').gsub(/&nbsp;/, ' ').strip %>"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>
            </span>
          <% end %>
        </div>
        <% if card.treatment.present? %>
          <div class="text-[13px] text-gray-500 mt-1 mb-3">
            <% if card.treatment.respond_to?(:nomenclature) && card.treatment.nomenclature.present? %>
              <%= card.treatment.nomenclature.truncate(50) %>
            <% elsif card.treatment.respond_to?(:patient_friendly_name) && card.treatment.patient_friendly_name.present? %>
              <%= card.treatment.patient_friendly_name.truncate(50) %>
            <% end %>
          </div>
        <% end %>
      </div>

      <button class="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700 relative z-20" data-dropdown-toggle="card-dropdown-<%= card.id %>" data-no-drag aria-expanded="false">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis w-4 h-4">
          <circle cx="12" cy="12" r="1"></circle>
          <circle cx="19" cy="12" r="1"></circle>
          <circle cx="5" cy="12" r="1"></circle>
        </svg>
      </button>
      
      <div id="card-dropdown-<%= card.id %>" class="absolute right-2 top-8 bg-white rounded-lg shadow-lg z-30 w-48 overflow-hidden hidden border border-gray-200" data-no-drag>
        <div class="py-1">
          <%= link_to admin_crm_card_path(card), class: "w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50" do %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye w-4 h-4 mr-2 text-gray-500">
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
            <span>View details</span>
          <% end %>
        </div>
        <div class="py-1 border-t border-gray-100">
          <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 text-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil w-4 h-4 mr-2 text-blue-500">
              <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
              <path d="m15 5 4 4"></path>
            </svg>
            <span>Edit patient</span>
          </button>
        </div>
        <div class="py-1 border-t border-gray-100">
          <button id="archive-btn-<%= card.id %>" class="archive-card-btn w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 text-amber-600 cursor-pointer" data-card-id="<%= card.id %>" onclick="if (window.archiveCard) { window.archiveCard('<%= card.id %>'); return false; }">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-archive w-4 h-4 mr-2 text-amber-600">
              <rect width="20" height="5" x="2" y="3" rx="1"></rect>
              <path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"></path>
              <path d="M10 12h4"></path>
            </svg>
            <span>Archive</span>
          </button>
        </div>
      </div>
    </div>
    
    <div class="flex flex-wrap gap-1 mb-3 card-tags-container" data-card-id="<%= card.id %>">
      <% if card.crm_labels.present? %>
        <% card.crm_labels.each_with_index do |label, index| %>
          <% color_classes = crm_label_color_classes(label) %>
          <% if index < 3 %>
            <span class="tag tag-circle w-3 h-3 rounded-full cursor-pointer transition-all duration-200 <%= color_classes %> shadow-sm" title="<%= label.name %>" data-label-id="<%= label.id %>" data-label-color="<%= color_classes %>" data-label-name="<%= label.name %>" data-no-drag="true"></span>
          <% else %>
            <span class="tag tag-circle w-3 h-3 rounded-full cursor-pointer transition-all duration-200 <%= color_classes %> shadow-sm hidden" title="<%= label.name %>" data-label-id="<%= label.id %>" data-label-color="<%= color_classes %>" data-label-name="<%= label.name %>" data-no-drag="true"></span>
          <% end %>
        <% end %>
        <% if card.crm_labels.size > 3 %>
          <span class="tag-more text-[10px] font-medium rounded-full cursor-pointer transition-all duration-200 overflow-hidden border bg-gray-100 text-gray-700 border-gray-200 px-1.5 shadow-sm" title="Show more tags" data-no-drag="true">+<%= card.crm_labels.size - 3 %></span>
        <% end %>
      <% end %>
    </div>
    <div class="flex flex-col space-y-1 text-[13px] text-gray-600">
      <% if card.patient&.email.present? %>
      <div class="flex items-center w-full overflow-hidden">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-3.5 h-3.5 mr-2 flex-shrink-0 text-gray-400">
          <rect width="20" height="16" x="2" y="4" rx="2"></rect>
          <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
        </svg>
        <span title="<%= card.patient.email %>"><%= truncate(card.patient.email, length: 22) %></span>
      </div>
      <% end %>
      <% if card.patient&.mobile_phone.present? %>
      <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone w-3.5 h-3.5 mr-2 text-gray-400">
          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
        </svg>
        <span><%= card.patient.mobile_phone %></span>
      </div>
      <% end %>
    </div>
    <% # Display custom fields that are set to show on card %>
    <% visible_custom_fields = card.available_custom_fields.where(show_on_card: true) %>
    <% 
      # Get visible custom fields with non-empty values
      visible_custom_fields_with_values = visible_custom_fields.select do |field|
        field_value = card.custom_field_value(field.id)
        field_value.present? && field_value.to_s.strip != ''
      end
    %>
    <% if visible_custom_fields_with_values.any? %>
      <div class="mt-3 pt-2 border-t border-gray-100">
        <div class="space-y-2">
          <% visible_custom_fields_with_values.each do |field| %>
            <% field_value = card.custom_field_value(field.id) %>
            <div class="flex items-center justify-between bg-gray-50 rounded-md px-2.5 py-1.5">
              <span class="text-[12px] text-gray-500"><%= field.name %></span>
              <span class="text-[12px] font-medium text-gray-700">
                <% if field.field_type == 'checkbox' %>
                  <%= field_value.to_s.downcase == 'true' ? 'Yes' : 'No' %>
                <% elsif field.field_type == 'number' && field.has_unit? %>
                  <% if field.unit_is_prefix? %>
                    <%= field.unit_symbol %><%= field_value %>
                  <% elsif field.unit_is_postfix? %>
                    <%= field_value %><%= field.unit_symbol %>
                  <% else %>
                    <%= field_value %>
                  <% end %>
                <% else %>
                  <%= field_value.to_s.truncate(20) %>
                <% end %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
    <% if card.crm_checklists.any? %>
      <% card.crm_checklists.each do |checklist| %>
        <div class="mt-3 pt-2 border-t border-gray-100">
          <div class="flex items-center justify-between mb-1.5">
            <span class="text-[12px] text-gray-500"><%= checklist.title %></span>
            <span class="text-[12px] font-medium text-gray-700"><%= checklist.completed_items_count %>/<%= checklist.items_count %></span>
          </div>
          <div class="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
            <div class="h-full bg-blue-300 rounded-full" style="width: <%= checklist.completion_percentage %>%;"></div>
          </div>
        </div>
      <% end %>
    <% end %>
    <div class="mt-3 pt-2 border-t border-gray-100 group relative z-10">
      <div class="flex flex-wrap justify-end -space-x-2 overflow-hidden">
        <% assigned_staff = card.patient&.assigned_staff || [] %>
        <% if assigned_staff.present? %>
          <% assigned_staff.each_with_index do |user, index| %>
            <div class="relative member" style="z-index: <%= 10 - index %>;">
              <%= render 'layouts/shared/user_avatar', user: user, width: 26, height: 26 %>
            </div>
          <% end %>
          <% if assigned_staff.size > 3 %>
            <div class="w-7 h-7 rounded-full bg-gray-500 text-white flex items-center justify-center text-[10px] font-medium border border-[#d1d1d6] shadow-sm">+<%= assigned_staff.size - 3 %></div>
          <% end %>
        <% else %>
          <div class="member text-[11px] text-gray-500">No team assigned</div>
        <% end %>
      </div>
    </div>
    <div class="mt-3 pt-2 border-t border-gray-100">
      <div class="bg-gray-50 rounded-lg px-3 py-2.5 flex items-center flex-nowrap min-w-0">
        <div class="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-3 h-3 text-blue-600">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>
        <span class="text-[13px] text-gray-700 whitespace-nowrap mr-2">Contacted</span><span class="text-[13px] font-medium text-gray-600 ml-auto whitespace-nowrap pr-2"><%= card.last_contacted_at ? precise_time_ago_in_words(card.last_contacted_at, include_seconds: true) + ' ago' : 'Never' %></span>
      </div>
    </div>
  </div>
