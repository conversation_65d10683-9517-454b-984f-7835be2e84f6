<div class="perio_index">
  <%= render 'admin/patients/header', patient: @patient %>

  <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 mx-4">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Perio Overview</h1>
      <p class="text-gray-600 mt-1">Perio charting, histories & analysis.</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
      <a class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" href="/admin/patients/<%= @patient.id %>/charting">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Back to Charting
      </a>
      <% if @perio_exams.count > 1 %>
        <%= link_to 'Compare Exam', compare_admin_patient_perio_exams_path(@patient.id), class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-green-100 hover:bg-green-200 text-green-700 border border-green-200 shadow-sm font-medium px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md' %>
      <% end %>
      <%= link_to 'New Exam', new_admin_patient_perio_exam_path(@patient.id), class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-blue-100 hover:bg-blue-200 text-blue-700 border border-blue-200 shadow-sm font-medium px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md' %>
    </div>
  </div>

  <div class="grid grid-cols-1 mx-4 xl:grid-cols-2 gap-6" id="perio-exams-carousel">
    <% @perio_exams.each do |perio| %>
      <div class="bg-white text-card-foreground shadow-sm border border-gray-100 rounded-lg overflow-hidden h-[calc(100vh-380px)]">
        <div class="p-0 h-full flex flex-col">
          <div class="grid grid-cols-2 divide-x divide-gray-100">
            <div class="p-4">
              <h2 class="text-sm font-semibold text-slate-800 mb-3 tracking-wide text-center">
                Perio Exam
              </h2>
              <div class="flex items-center justify-center gap-3">
                <div class="bg-gradient-to-br from-slate-50 to-gray-100 rounded-xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-300 p-3 group flex-shrink-0">
                  <div class="w-12 h-14 bg-gradient-to-b from-white to-slate-100 rounded-lg shadow-lg border border-slate-200/80 flex items-center justify-center relative overflow-hidden group-hover:scale-105 transition-transform duration-200">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30"></div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file h-6 w-6 text-slate-600 relative z-10">
                      <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                      <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    </svg>
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-slate-600 to-slate-700 text-white text-[9px] font-medium text-center py-1 tracking-wider">PDF</div>
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <a href="<%= "/admin/patients/#{@patient.id}/perio_exams/#{perio.id}" %>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border rounded-md px-3 border-slate-300/60 bg-white/80 text-slate-700 hover:bg-slate-50 hover:text-slate-800 hover:border-slate-400/60 h-9 font-medium tracking-wide shadow-sm hover:shadow transition-all duration-200 text-xs">
                    View perio exam
                  </a>
                  <div class="flex items-center gap-2">
                    <%= render 'layouts/shared/user_avatar', user: perio.provided_by, width: 24, height: 24 %>
                    <span class="inline-flex items-center rounded-full px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-blue-50 text-blue-600 border-0 hover:bg-blue-50 text-xs">
                      <%= perio.provided_by.full_name %>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-3 flex flex-col items-center justify-center text-center">
              <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mb-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 text-gray-600">
                  <path d="M8 2v4"></path>
                  <path d="M16 2v4"></path>
                  <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                  <path d="M3 10h18"></path>
                </svg>
              </div>
              <h3 class="text-xs text-gray-800 font-medium">
                Created Date
              </h3>
              <p class="text-sm font-semibold text-gray-900">
                <%= perio.updated_at.strftime("%d %b, %Y") %>
              </p>
              <p class="text-gray-500 text-xs">
                <% today = Date.today %>
                <% month_difference = (today.year * 12 + today.month) - (perio.updated_at.year * 12 + perio.updated_at.month) %>
                <%= month_difference %> months ago
              </p>
            </div>
          </div>

          <div class="grid grid-cols-2 divide-x divide-gray-100 border-t border-gray-100 flex-1 min-h-0">
            <div class="p-6 flex flex-col h-full min-h-0">
              <div class="mb-4 flex items-center">
                <h2 class="text-sm font-medium text-gray-800">
                  Alerts
                </h2>
                <div class="inline-flex text-white items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-primary-foreground ml-2 bg-rose-500 hover:bg-rose-500">
                  <%= perio.perio_alerts.count %>
                </div>
              </div>
              <%= render "perio_alerts", perio: perio %>
            </div>

            <div class="p-6 flex flex-col h-full min-h-0">
              <div class="mb-4 flex items-center">
                <h2 class="text-sm font-medium text-gray-800">
                  Changes
                </h2>
                <div class="inline-flex text-white items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-primary-foreground ml-2 bg-rose-500 hover:bg-rose-500">
                  <%= perio.perio_changes.count %>
                </div>
              </div>
              <%= render "perio_changes", perio: perio, perio_exams: @perio_exams %>
            </div>
          </div>

          <div class="p-6 border-t border-gray-100 flex-shrink-0">
            <div class="mb-4 flex items-center">
              <h2 class="text-sm font-medium text-gray-800">
                Summary
              </h2>
            </div>
            <%= render "perio_summary", perio: perio %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
