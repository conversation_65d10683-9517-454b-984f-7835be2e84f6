<div class="space-y-4 flex-1 overflow-y-auto max-h-[calc(100vh-800px)]" id="mh_changes_<%= history.id %>">
  <% if history.ai_changes_done && history.medical_history_ai_changes.blank? %>
    <!-- No Changes State -->
    <div class="p-4 bg-white border border-gray-100 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-500">
              <path d="M9 12l2 2 4-4"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800">No changes detected in medical history.</p>
        </div>
      </div>
    </div>
  <% end %>

  <% history.medical_history_ai_changes.each do |change| %>
    <!-- Change Item -->
    <div class="p-4 bg-white border border-gray-100 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4 text-gray-500">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800"><%= change.change_text %></p>
        </div>
      </div>
    </div>
  <% end %>

  <% unless history.ai_changes_done %>
    <!-- Loading State -->
    <div class="p-4 bg-blue-50 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
            <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800">Analyzing medical history for changes...</p>
        </div>
      </div>
    </div>
  <% end %>
</div>
