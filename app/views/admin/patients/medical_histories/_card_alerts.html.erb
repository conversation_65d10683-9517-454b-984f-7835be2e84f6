<div class="space-y-4 flex-1 overflow-y-auto max-h-[calc(100vh-800px)]" id="mh_alerts_<%= history.id %>">
  <% if history.ai_alerts_done && history.medical_history_ai_alerts.blank? %>
    <!-- No Alerts State -->
    <div class="p-4 bg-gray-50 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-500">
              <path d="M9 12l2 2 4-4"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800 leading-relaxed">No medical alerts found for this patient.</p>
        </div>
      </div>
    </div>
  <% end %>

  <% history.medical_history_ai_alerts.each do |alert| %>
    <!-- Alert Item -->
    <div class="p-4 bg-rose-50 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-rose-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-alert h-4 w-4 text-rose-500">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" x2="12" y1="8" y2="12"></line>
              <line x1="12" x2="12.01" y1="16" y2="16"></line>
            </svg>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800 leading-relaxed"><%= alert.alert_text %></p>
        </div>
      </div>
    </div>
  <% end %>

  <% unless history.ai_alerts_done %>
    <!-- Loading State -->
    <div class="p-4 bg-blue-50 rounded-lg">
      <div class="flex gap-4">
        <div class="mt-1 flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
            <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
        <div>
          <p class="text-xs text-gray-800 leading-relaxed">Analyzing medical history for potential alerts...</p>
        </div>
      </div>
    </div>
  <% end %>
</div>
