<tr class="action-type-<%= action.action_type %><% if action.deleted? %> action-type-archived<% end %>"<% if action.deleted? %> style="display: none;"<% end %>
    <% if action.assigned_to_appointment? %>data-appointment-id="<%= action.appointment_id %>"<% end %>>
  <td><%= action_icon(action) %></td>
  <td><%= action.patient&.full_name %></td>
  <td>
    <div class="d-flex align-items-center">
      <div class="me-3">
        <div class="text-center mb-1">
          <%= render "layouts/shared/user_avatar", user: action.created_by, width: 32, height: 32, tippy: true %>
        </div>
        <div class="text-center"><small class="text-muted">Creator</small></div>
        <div class="text-center"><small class="text-muted date-info">Created: <%= action.created_at.strftime("%d %b %Y") %></small></div>
      </div>

      <% if action.assigned_to_json.count > 0 %>
        <i class="far fa-arrow-right px-2"></i>

        <div class="d-flex flex-column">
          <div class="d-flex">
            <% action.assigned_to.each do |assigned| %>
              <div class="me-2 text-center">
                <%= render "layouts/shared/user_avatar", user: assigned, width: 32, height: 32, tippy: true %>
              </div>
            <% end %>
          </div>
          <div><small class="text-muted">Assigned</small></div>
          <% if action.assigned_to_appointment? %>
            <% if action.appointment&.start_time.present? %>
              <% if action.appointment_booked? %>
                <div><small class="text-muted date-info">Due: <%= action.appointment.start_time.strftime("%d %b %Y at %H:%M") %></small></div>
                <div><small class="text-info">Appointment: <%= action.appointment_status %></small></div>
              <% else %>
                <div><small class="text-warning">Appointment unbooked</small></div>
                <div><small class="text-muted">Due when scheduled</small></div>
              <% end %>
            <% else %>
              <div><small class="text-warning">Appointment unbooked</small></div>
              <div><small class="text-muted">Due when scheduled</small></div>
            <% end %>
          <% elsif action.date_due %>
            <div><small class="text-muted date-info">Due: <%= action.date_due.strftime("%d %b %Y") %></small></div>
          <% end %>
        </div>
      <% end %>
    </div>
  </td>
  <td><%= action.title %></td>
  <td>
    <%= action_priority_badge(action) %>
  </td>
  <td style="max-width: 400px;"><span class="truncate" style="--truncate-lines: 2;" data-title="Description"><%= action.description %></span></td>
  <td><%= action.completed ? "Completed" : "Uncompleted" %></td>
  <td>
    <div class="dropdown float-end">
     <%= render "admin/shared/dropdown_btn" %>
     <ul class="dropdown-menu">
      <li><a href="<%= mark_as_completed_admin_action_path(action) %>" class="dropdown-item">Mark as <%= action.completed ? "uncompleted" : "completed" %></a></li>
      <li><a href="#" class="dropdown-item action-reply" data-action-id="<%= action.id %>">Reply to action</a></li>
      <li><a href="#" class="dropdown-item action-timeline" data-action-id="<%= action.id %>">Show action timeline</a></li>
      <li><hr class="dropdown-divider"></li>
      <li><a href="<%= admin_action_path(action) %>" data-method="delete" class="dropdown-item text-danger">Archive action</a></li>
     </ul>
    </div>
   <% if action.action_comments.count > 0 %>
    <button class="btn btn-primary p-1 rounded-circle action-show-comments float-end me-2">
     <i class="fa-thin fa-message-lines"></i>
    </button>
   <% end %>
  </td>
</tr>
<% comments.each do |comment| %>
 <%= render "admin/actions/comment_row", action: action, comment: comment %>
<% end %>