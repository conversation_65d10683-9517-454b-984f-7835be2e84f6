<% if @actions.present? %>
  <% @actions.each do |action| %>
    <% 
      is_today = action.date_due.present? && action.date_due.to_date == Date.today 
      date_class = is_today ? "today-action" : "other-action"
      
      # Set color scheme based on action type
      color_scheme = case action.action_type
                    when "task"
                      {
                        border: "from-blue-400 to-blue-500",
                        icon_bg: "bg-blue-100",
                        icon_text: "text-blue-600",
                        icon: "list-check"
                      }
                    when "reminder"
                      {
                        border: "from-amber-400 to-amber-500",
                        icon_bg: "bg-amber-100",
                        icon_text: "text-amber-600",
                        icon: "bell"
                      }
                    when "alerts"
                      {
                        border: "from-red-400 to-red-500",
                        icon_bg: "bg-red-100",
                        icon_text: "text-red-600",
                        icon: "alert-triangle"
                      }
                    when "callback"
                      {
                        border: "from-green-400 to-green-500",
                        icon_bg: "bg-green-100",
                        icon_text: "text-green-600",
                        icon: "phone"
                      }
                    else
                      {
                        border: "from-gray-400 to-gray-500",
                        icon_bg: "bg-gray-100",
                        icon_text: "text-gray-600",
                        icon: "circle"
                      }
                    end
      
      # Set priority style
      priority_style = case action.priority
                      when "high"
                        {
                          bg: "bg-red-50/80",
                          text: "text-red-700",
                          border: "border-red-200/60"
                        }
                      when "medium"
                        {
                          bg: "bg-amber-50/80",
                          text: "text-amber-700",
                          border: "border-amber-200/60"
                        }
                      else
                        {
                          bg: "bg-blue-50/80",
                          text: "text-blue-700",
                          border: "border-blue-200/60"
                        }
                      end
    %>
    <div class="mb-8 action-card <%= date_class %>" data-action-type="<%= action.action_type %>" data-date="<%= action.date_due.present? ? action.date_due.to_date : '' %>"
         <% if action.assigned_to_appointment? %>data-appointment-id="<%= action.appointment_id %>"<% end %>>
      <div class="action-card-container bg-gradient-to-b from-white to-gray-50/80 backdrop-blur-xl rounded-xl border border-gray-200/60 shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)] overflow-hidden flex transition-all duration-300 hover:shadow-[0_8px_30px_-4px_rgba(0,0,0,0.1)] hover:border-gray-300/60" data-action-id="<%= action.id %>">
        <div class="w-1.5 bg-gradient-to-b <%= color_scheme[:border] %> rounded-tl-xl rounded-bl-xl"></div>
        <div class="flex-1">
          <div class="px-4 pt-4 pb-2 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="w-6 h-6 rounded-full <%= color_scheme[:icon_bg] %> flex items-center justify-center">
                <% if color_scheme[:icon] == "list-check" %>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list-checks h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                    <path d="m3 7 3 3 3-3"></path>
                    <path d="M3 14h6"></path>
                    <path d="m3 21 3-3 3 3"></path>
                    <path d="M11 5h10"></path>
                    <path d="M11 12h10"></path>
                    <path d="M11 19h10"></path>
                  </svg>
                <% elsif color_scheme[:icon] == "bell" %>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                  </svg>
                <% elsif color_scheme[:icon] == "alert-triangle" %>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                    <path d="M12 9v4"/>
                    <path d="M12 17h.01"/>
                  </svg>
                <% elsif color_scheme[:icon] == "phone" %>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                  </svg>
                <% else %>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                    <circle cx="12" cy="12" r="10"/>
                  </svg>
                <% end %>
              </div>
              <span class="text-sm font-semibold uppercase text-gray-500"><%= action.action_type.capitalize %></span>
            </div>
            <div class="flex items-center gap-2">
              <% if action.priority.present? %>
                <div class="inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-full <%= priority_style[:bg] %> <%= priority_style[:text] %> <%= priority_style[:border] %> backdrop-blur-sm" data-v0-t="badge">
                  <%= action.priority.capitalize %>
                </div>
              <% end %>
              
              <% if action.assigned_to_appointment? %>
                <% if action.appointment&.start_time.present? %>
                  <% if action.appointment_booked? %>
                    <%
                      days_overdue = action.days_until_due
                      overdue_class = days_overdue < 0 ? "text-red-600" : "text-green-600"
                      overdue_text = days_overdue < 0 ? "#{days_overdue.abs} days overdue" : "#{days_overdue} days remaining"
                    %>
                    <span class="text-xs font-medium px-2 py-1 rounded-full bg-blue-50/80 text-blue-700 border border-blue-200/60 backdrop-blur-sm flex items-center gap-1">
                      <i class="fa-light fa-tooth mr-1"></i>
                      Due: <%= action.appointment.start_time.strftime("%d %b %Y at %H:%M") %>
                      <span class="inline-block h-1 w-1 bg-blue-300 rounded-full mx-1"></span>
                      <span class="font-semibold <%= overdue_class %>"><%= overdue_text %></span>
                    </span>
                  <% else %>
                    <span class="text-xs font-medium px-2 py-1 rounded-full bg-orange-50/80 text-orange-700 border border-orange-200/60 backdrop-blur-sm flex items-center gap-1">
                      <i class="fa-light fa-tooth mr-1"></i>
                      Appointment unbooked - Due when scheduled
                    </span>
                  <% end %>
                <% else %>
                  <span class="text-xs font-medium px-2 py-1 rounded-full bg-orange-50/80 text-orange-700 border border-orange-200/60 backdrop-blur-sm flex items-center gap-1">
                    <i class="fa-light fa-tooth mr-1"></i>
                    Appointment unbooked - Due when scheduled
                  </span>
                <% end %>
              <% elsif action.date_due.present? %>
                <%
                  days_overdue = (Date.today - action.date_due.to_date).to_i
                  overdue_class = days_overdue > 0 ? "text-red-600" : "text-green-600"
                  overdue_text = days_overdue > 0 ? "#{days_overdue} days overdue" : "#{days_overdue.abs} days remaining"
                %>
                <span class="text-xs font-medium px-2 py-1 rounded-full bg-amber-50/80 text-amber-700 border border-amber-200/60 backdrop-blur-sm flex items-center gap-1">
                  Due: <%= action.date_due.strftime("%d %b %Y") %>
                  <span class="inline-block h-1 w-1 bg-amber-300 rounded-full mx-1"></span>
                  <span class="font-semibold <%= overdue_class %>"><%= overdue_text %></span>
                </span>
              <% end %>
            </div>
          </div>
          <div class="p-4">
            <div class="flex justify-between items-start">
              <div>
                <% if action.actionable.present? && action.actionable.respond_to?(:name) %>
                  <h3 class="font-medium mb-1"><%= action.actionable.name %></h3>
                <% elsif action.patient.present? %>
                  <h3 class="font-medium mb-1"><%= action.patient.full_name %></h3>
                <% else %>
                  <h3 class="font-medium mb-1">Action #<%= action.id %></h3>
                <% end %>
              </div>
              <div class="group relative flex items-center gap-1">
                <% if action.created_by_id.present? && User.find_by(id: action.created_by_id).present? %>
                  <% creator = User.find_by(id: action.created_by_id) %>
                  <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                    <div class="flex items-center justify-center w-full h-full bg-gray-200 text-gray-600">
                      <%= creator.respond_to?(:initials) ? creator.initials : creator.email.first(2).upcase %>
                    </div>
                  </span>
                <% end %>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-chevron-right h-4 w-4 text-gray-400">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
                
                <div class="flex -space-x-2">
                  <% if action.assigned_to_json.present? %>
                    <% 
                      begin
                        # Handle both string and array formats
                        assigned_users = if action.assigned_to_json.is_a?(String)
                          JSON.parse(action.assigned_to_json).reject(&:blank?).first(3)
                        elsif action.assigned_to_json.is_a?(Array)
                          action.assigned_to_json.reject(&:blank?).first(3)
                        else
                          []
                        end
                      rescue JSON::ParserError
                        assigned_users = []
                      end
                    %>
                    <% assigned_users.each do |user_id| %>
                      <% user = User.find_by(id: user_id) %>
                      <% if user.present? %>
                        <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                          <div class="flex items-center justify-center w-full h-full bg-gray-200 text-gray-600">
                            <%= user.respond_to?(:initials) ? user.initials : user.email.first(2).upcase %>
                          </div>
                        </span>
                      <% end %>
                    <% end %>
                    
                    <% 
                      begin
                        # Handle both string and array formats for total count
                        total_assigned = if action.assigned_to_json.is_a?(String)
                          JSON.parse(action.assigned_to_json).reject(&:blank?).size
                        elsif action.assigned_to_json.is_a?(Array)
                          action.assigned_to_json.reject(&:blank?).size
                        else
                          0
                        end
                      rescue JSON::ParserError
                        total_assigned = 0
                      end
                    %>
                    <% if total_assigned > 3 %>
                      <div class="flex items-center justify-center h-8 w-8 bg-gray-100 rounded-full border-2 border-white shadow-sm">
                        <span class="text-xs text-gray-500">+<%= total_assigned - 3 %></span>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
            
            <% if action.description.present? %>
              <p class="text-sm text-gray-600 mb-4 mt-4 w-full"><%= action.description %></p>
            <% else %>
              <p class="text-sm text-gray-400 italic mb-4 mt-4 w-full">No description provided</p>
            <% end %>
          </div>
          <div class="flex items-center justify-between gap-2 p-2 bg-gray-50">
            <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-8 w-8 rounded-full relative backdrop-blur-sm bg-gradient-to-r from-blue-50/80 to-blue-100/60 text-blue-500 hover:text-blue-600 hover:bg-blue-100/70 border border-blue-100/40 transition-all duration-200 shadow-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </button>
            <div class="flex items-center gap-2">
              <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:text-accent-foreground px-3 h-8 rounded-full relative backdrop-blur-sm hover:bg-gray-100/80 border-gray-200/60">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-3.5 w-3.5 mr-1">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>Comments
                <% comment_count = action.action_comments.where.not(comment: [nil, '']).count %>
                <% if comment_count > 0 %>
                  <span class="absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white"><%= comment_count %></span>
                <% end %>
              </button>
              <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full text-amber-600 border-amber-200/70 bg-amber-50/80 hover:bg-amber-100/80 backdrop-blur-sm transition-all duration-200" type="button" id="reminder-dropdown-<%= action.id %>" aria-haspopup="menu" aria-expanded="false" data-state="closed" data-action-id="<%= action.id %>">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3.5 w-3.5 mr-1">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>Reminder
              </button>
              <button data-action-id="<%= action.id %>" data-action-type="<%= action.action_type %>" class="complete-action-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full text-emerald-600 border-emerald-200 bg-emerald-50/80 hover:bg-emerald-100/80 backdrop-blur-sm transition-all duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big h-3.5 w-3.5 mr-1.5">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <path d="m9 11 3 3L22 4"></path>
                </svg><%= %w[complaint alert].include?(action.action_type) ? 'Resolve' : 'Complete' %>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
<% else %>
  <div class="empty-state flex flex-col items-center justify-center p-8 text-center">
    <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-8 w-8 text-gray-400">
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
        <polyline points="22 4 12 14.01 9 11.01"/>
      </svg>
    </div>
    <h3 class="text-lg font-medium text-gray-900 mb-1">No actions found</h3>
    <p class="text-sm text-gray-500">This user has no pending actions</p>
  </div>
<% end %>
