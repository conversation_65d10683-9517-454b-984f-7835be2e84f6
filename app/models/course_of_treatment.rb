# frozen_string_literal: true

# == Schema Information
#
# Table name: course_of_treatments
#
#  id                       :bigint           not null, primary key
#  name                     :string
#  patient_id               :bigint           not null
#  dentist_id               :bigint           not null
#  treatment_coordinator_id :bigint
#  lead_clinician_id        :bigint           not null
#  practice_id              :bigint           not null
#  treatment_plan_notes     :text
#  archived                 :boolean          default(FALSE)
#  archived_at              :datetime
#  completed_at             :datetime
#  charged_date             :datetime
#  accepted                 :boolean          default(FALSE)
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  import_id                :string
#  import_source            :integer
#  import_data              :jsonb
#  cot_category_id          :bigint
#
class CourseOfTreatment < ApplicationRecord
  include EventLoggable
  include Importable

  belongs_to :patient
  belongs_to :dentist,               class_name: 'User'
  belongs_to :treatment_coordinator, class_name: 'User', optional: true
  belongs_to :lead_clinician,        class_name: 'User'
  belongs_to :practice
  belongs_to :cot_category, optional: true

  has_many :charting_appointments, dependent: :nullify
  has_many :charted_treatments, through: :charting_appointments

  has_many :xrays, dependent: :destroy
  has_many :cbcts, dependent: :destroy
  has_many :opgs, dependent: :destroy
  has_many :clinical_images, dependent: :destroy
  has_many :invoices

  has_many :treatment_plan_options, dependent: :nullify

  accepts_nested_attributes_for :charting_appointments, allow_destroy: true, reject_if: :all_blank

  scope :accepted, -> { where(accepted: true) }
  scope :without_archived, -> { where(archived: false) }

  after_create :assign_lead_clinician_to_patient

  def allocated?
    false
  end

  def total_price
    charted_treatments.includes(:treatment).sum { |ch_t| ch_t.price.to_f }
  end

  def assign_lead_clinician_to_patient
    return if patient.assigned_staff.include?(lead_clinician)

    patient.assigned_staff << lead_clinician
  end
end
