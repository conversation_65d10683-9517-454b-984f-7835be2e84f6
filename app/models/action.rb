# frozen_string_literal: true

# == Schema Information
#
# Table name: actions
#
#  id                :bigint           not null, primary key
#  action_type       :string           default("task")
#  actionable_type   :string           not null
#  actionable_id     :bigint           not null
#  created_by_id     :bigint
#  assigned_to_json  :json
#  title             :string
#  description       :text
#  date_due          :datetime
#  completed         :boolean          default(FALSE)
#  priority          :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  deleted_at        :datetime
#  assignment_type   :string
#  treatment_plan_id :bigint
#  appointment_id    :bigint
#
class Action < ApplicationRecord
  include IconStateObservable

  acts_as_paranoid

  belongs_to :actionable, polymorphic: true
  belongs_to :created_by, class_name: 'User', optional: true
  belongs_to :treatment_plan, class_name: 'CourseOfTreatment', optional: true
  belongs_to :appointment, class_name: 'CalendarBooking', optional: true
  has_many :action_comments

  # after_create :set_ai_title
  after_create :create_notifications
  after_save :trigger_automations
  after_save :update_actions_counter
  before_validation :set_default_priority

  validates :priority, inclusion: { in: %w[low medium high urgent], message: 'must be low, medium, high, or urgent' }

  scope :complaints, -> { where(action_type: 'complaint') }
  scope :by_priority, ->(priority) { where(priority: priority) }
  scope :urgent, -> { where(priority: 'urgent') }
  scope :high_priority, -> { where(priority: 'high') }
  scope :medium_priority, -> { where(priority: 'medium') }
  scope :low_priority, -> { where(priority: 'low') }
  scope :due_today_or_no_date, -> {
    joins('LEFT JOIN calendar_bookings ON calendar_bookings.id = actions.appointment_id')
      .where(
        'date_due IS NULL OR DATE(date_due) = ? OR ' \
        '(appointment_id IS NOT NULL AND calendar_bookings.start_time IS NOT NULL AND DATE(calendar_bookings.start_time) = ?)',
        Date.current, Date.current
      )
  }
  scope :incomplete, -> { where(completed: false) }

  def set_ai_title
    return unless practice.present? && practice.azure_configured?

    service = Ai::ActionService.new(practice)
    new_title = service.generate_title(self)
    update(title: new_title)
  end

  def create_notifications
    # Create a toast notification for all action types
    assigned_to.each do |user|
      actions = get_notification_actions_for_type
      title = get_title_for_type
      # Create the notification
      icon_class = Admin::ActionsController.helpers.action_icon_class(action_type, priority).to_s
      icon = icon_class.present? ? icon_class.delete_prefix('fa-light fa-') : 'bell'

      notification = Notification.create(
        recipient: user,
        title: title,
        description: action_type == 'complaint' ? get_complaint_description : description,
        icon: icon,
        actions: actions,
        data: { type: 'actions', style: action_type }
      )

      # Log notification creation for debugging
      Rails.logger.info "Created notification ##{notification.id} for action type: #{action_type}, recipient: #{user.email}"
    end
  end

  def get_notification_actions_for_type
    base_actions = [
      { text: 'Remind in', action: 'remind_in', id: id },
      { text: 'Mark Read', action: 'mark_as_read', id: id }
    ]

    base_actions << case action_type
                    when 'task'
                      { text: 'Task Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'reminder'
                      { text: 'Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'alerts'
                      { text: 'Close', action: 'mark_as_completed', id: id, primary: true }
                    when 'callback'
                      { text: 'Callback Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'complaint'
                      { text: 'Complaint Status', action: 'view_action', id: id, primary: true }
                    else
                      { text: 'View', action: 'view_action', id: id, primary: true }
                    end

    base_actions
  end

  def get_title_for_type
    name = actionable.full_name
    case action_type
    when 'task'
      "New task created for #{name}#{date_due.present? ? " due #{format_due_date(date_due)}" : ''}"
    when 'reminder'
      "New reminder created for #{name}#{date_due.present? ? " due #{format_due_date(date_due)}" : ''}"
    when 'alerts'
      "New alert created for #{name}"
    when 'callback'
      "New callback created for #{name}#{date_due.present? ? " due #{format_due_date(date_due)}" : ''}"
    when 'complaint'
      "New complaint logged for #{name}"
    else
      "New action created for #{name}"
    end
  end

  def get_complaint_description
    name = actionable.full_name
    <<~HTML.strip
      #{name} has an active complaint recorded on their account. Please review the details before proceeding with any interaction, and ensure all communication and treatment are handled with extra care and professionalism. Any updates or incidents should be documented promptly and escalated to the appropriate team if necessary.
    HTML
  end

  def format_due_date(date)
    return 'today' if date.to_date == Date.current

    day = date.strftime('%A')
    day_number = date.day
    month = date.strftime('%B')

    ordinal = ActiveSupport::Inflector.ordinalize(day_number)

    "#{day} #{ordinal} #{month}"
  end

  def patient
    if actionable_type == 'Patient'
      actionable
    else
      actionable.respond_to?(:patient) ? actionable.patient : nil
    end
  end

  def practice
    if actionable.respond_to?(:practice) && actionable.practice.present?
      actionable.practice
    elsif patient.present? && patient.respond_to?(:practices) && patient.practices.any?
      patient.practices.first
    end
  end

  def assigned_to
    User.where(id: assigned_to_json)
  end

  def trigger_automations
    Automations::TriggerProcessor.call(model: self, event_type: 'action_assigned') if saved_changes[:assigned_to_json]
  end

  # Check if this action is assigned to an appointment
  def assigned_to_appointment?
    appointment_id.present?
  end

  # Check if the assigned appointment is booked (has a patient assigned)
  def appointment_booked?
    return false unless assigned_to_appointment?

    appointment&.patient_id.present?
  end

  # Get the appointment status for display
  def appointment_status
    return nil unless assigned_to_appointment?

    if appointment_booked?
      appointment.status.humanize
    else
      'Unbooked'
    end
  end

  # Get the effective due date - either the set date_due or the appointment date
  def effective_due_date
    if assigned_to_appointment? && appointment&.start_time.present?
      appointment.start_time
    else
      date_due
    end
  end

  # Get display text for the due date considering appointment status
  def due_date_display
    if assigned_to_appointment?
      if appointment&.start_time.present?
        if appointment_booked?
          "Due: #{appointment.start_time.strftime('%d/%m/%Y at %H:%M')}"
        else
          "Appointment unbooked - Due when scheduled"
        end
      else
        "Appointment unbooked - Due when scheduled"
      end
    elsif date_due.present?
      "Due: #{date_due.strftime('%d/%m/%Y')}"
    else
      "No due date"
    end
  end

  # Check if the action is overdue considering appointment status
  def overdue?
    effective_date = effective_due_date
    return false unless effective_date.present?

    effective_date < Time.current
  end

  # Get days until due (negative if overdue)
  def days_until_due
    effective_date = effective_due_date
    return nil unless effective_date.present?

    (effective_date.to_date - Date.current).to_i
  end

  def self.notification_count_for_user(user)
    return 0 unless user

    # Get actions assigned to the user or created by the user
    assigned_action_ids = where(deleted_at: nil)
                          .where('assigned_to_json::text LIKE ?', "%#{user.id}%")
                          .pluck(:id)

    where(id: assigned_action_ids)
      .or(where(created_by_id: user.id))
      .incomplete
      .due_today_or_no_date
      .count
  end

  def update_actions_counter
    # Update counter for all users who are assigned to this action or created it
    users_to_update = Set.new
    users_to_update.add(created_by) if created_by.present?
    assigned_to.each { |user| users_to_update.add(user) }

    users_to_update.each do |user|
      next unless user.is_a?(User)

      pusher = PusherService.new
      # Count only actions assigned TO the user (their actual workload)
      assigned_action_ids = Action.where(deleted_at: nil)
                                  .where('assigned_to_json::text LIKE ?', "%#{user.id}%")
                                  .pluck(:id)

      actions_count = Action.where(id: assigned_action_ids)
                            .incomplete
                            .count

      pusher.replace(
        "private-notifications.#{user.id}",
        '#actions_count',
        partial: 'layouts/admin/counter', locals: { count: actions_count, id: 'actions_count' }
      )
    end
  end

  private

  def icon_state_observable_records
    if actionable_type == 'Patient'
      actionable.icon_state_related_records
    else
      []
    end
  end

  def set_default_priority
    self.priority = 'low' if priority.blank?
  end
end
