# frozen_string_literal: true

# == Schema Information
#
# Table name: patients
#
#  id                               :bigint           not null, primary key
#  first_name                       :string
#  last_name                        :string
#  middle_name                      :string
#  email                            :string
#  title                            :string
#  previous_last_name               :string
#  preferred_name                   :string
#  date_of_birth                    :date
#  ethnicity                        :string
#  biological_sex                   :string
#  gender                           :string
#  pronouns                         :string
#  ni_number                        :string
#  insurance_number                 :string
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  mobile_phone                     :string
#  postcode                         :string
#  sms_consent                      :boolean          default(FALSE)
#  email_consent                    :boolean          default(FALSE)
#  whatsapp_consent                 :boolean          default(FALSE)
#  instagram_consent                :boolean          default(FALSE)
#  facebook_consent                 :boolean          default(FALSE)
#  marketing_consent                :boolean          default(FALSE)
#  hide_base_and_history_treatments :boolean          default(FALSE)
#  treatment_plan_code              :string
#  treatment_plan_verification_code :string
#  pin                              :string
#  address_line_1                   :string
#  address_line_2                   :string
#  city                             :string
#  county                           :string
#  country                          :string           default("GB")
#  encrypted_password               :string           default("")
#  reset_password_token             :string
#  reset_password_sent_at           :datetime
#  remember_created_at              :datetime
#  force_password_reset             :boolean          default(FALSE)
#  email_verification_code          :string
#  school_name                      :string
#  school_phone_number              :string
#  school_address_line_1            :string
#  school_address_line_2            :string
#  school_county                    :string
#  school_country                   :string           default("GB")
#  school_city                      :string
#  school_postcode                  :string
#  patient_gp_id                    :bigint
#  archived                         :boolean          default(FALSE), not null
#  import_id                        :string
#  import_source                    :integer
#  import_data                      :jsonb
#  opening_balance                  :decimal(10, 2)   default(0.0)
#  active_cot_payment_plan_id       :bigint
#  diagnocat_id                     :string
#  current_practice_id              :bigint
#  consent_updated_at               :datetime
#  occupation                       :string
#  emergency_contact_name           :string
#  emergency_contact_number         :string
#  alternative_phone                :string
#  work_phone                       :string
#  temporary                        :boolean          default(FALSE)
#  created_by_id                    :integer
#  on_stop                          :boolean          default(FALSE), not null
#  secure_code_sent_at              :datetime
#  entered_into_portal_at           :datetime
#  verification_method              :string           default("")
#  witnessed_by_id                  :bigint
#
class Patient < ApplicationRecord
  include EventLoggable
  include Importable

  # Scopes for temporary patients
  scope :temporary, -> { where(temporary: true) }
  scope :permanent, -> { where(temporary: false) }

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, authentication_keys: [:login]

  # Custom validations to replace :validatable module
  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?
  validates :password, confirmation: true, if: :password_required?
  validates :email, format: { with: Devise.email_regexp }, allow_blank: true
  has_many :practices_patients, dependent: :destroy
  has_many :practices, through: :practices_patients

  belongs_to :current_practice, class_name: 'Practice', optional: true
  belongs_to :witnessed_by, class_name: 'User', optional: true

  has_many :patients_users, dependent: :destroy
  has_many :assigned_staff, through: :patients_users, source: :user

  has_many :patient_cot_payment_plans, dependent: :destroy
  has_many :cot_payment_plans, through: :patient_cot_payment_plans

  belongs_to :active_cot_payment_plan, class_name: 'CotPaymentPlan', optional: true

  has_many :registered_devices, dependent: :destroy
  has_many :patient_notes, dependent: :destroy
  has_many :course_of_treatments
  has_many :invoices
  has_many :payments
  has_many :treatment_plan_options, dependent: :destroy
  has_many :treatment_plans, through: :treatment_plan_options, dependent: :destroy
  has_many :calendar_bookings
  has_many :booked_bookings, as: :booked_by, class_name: 'CalendarBooking'
  has_many :recalls, dependent: :destroy
  has_many :recall_schedules, dependent: :destroy
  has_many :lab_works, dependent: :destroy
  has_many :perio_exams, dependent: :destroy
  has_many :bpes, dependent: :destroy
  has_many :bewes, dependent: :destroy

  has_one :base_chart

  has_many :xrays, dependent: :destroy
  has_many :cbcts, dependent: :destroy
  has_many :opgs, dependent: :destroy
  has_many :stls, dependent: :destroy
  has_many :charting_files, dependent: :destroy
  has_many :clinical_images, dependent: :destroy

  has_many :prescriptions

  has_many :medical_histories, dependent: :destroy

  has_one_attached :image

  has_one :conversation

  has_many :patient_assets
  has_many :actions, as: :actionable, dependent: :destroy
  has_many :crm_cards, dependent: :nullify
  has_many :signature_requests
  has_many :letters
  has_many :payment_plans

  has_many :patient_alerts, dependent: :destroy
  has_many :alerts, through: :patient_alerts
  belongs_to :patient_gp, optional: true

  scope :fuzzy_search, lambda { |query|
    return all if query.blank?

    # Clean and split the query
    words = query.strip.split(/\s+/)

    # Handle different search logic based on number of words
    conditions = case words.length
                 when 1
                   # Single word: search across both first and last names
                   word = words.first
                   sanitized_word = "%#{word.downcase}%"
                   "(LOWER(patients.first_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_word)}
        OR LOWER(patients.last_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_word)})"
                 when 2
                   # Two words: first word searches first names, second word searches last names
                   first_word = words.first
                   second_word = words.last
                   sanitized_first_word = "%#{first_word.downcase}%"
                   sanitized_second_word = "%#{second_word.downcase}%"
                   "(LOWER(patients.first_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_first_word)}
        AND LOWER(patients.last_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_second_word)})"
                 else
                   # Three or more words: fall back to original behavior (all words must match somewhere)
                   words.map do |search_word|
                     sanitized_word = "%#{search_word.downcase}%"
                     "(LOWER(patients.first_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_word)}
          OR LOWER(patients.last_name) LIKE #{ActiveRecord::Base.connection.quote(sanitized_word)})"
                   end.join(' AND ')
                 end

    # Apply conditions and order by name (similarity scoring causes issues with count queries)
    where(conditions).order('patients.first_name ASC, patients.last_name ASC')
  }

  scope :without_archived, -> { where(archived: false) }
  scope :archived, -> { where(archived: true) }
  scope :on_stop, -> { where(on_stop: true) }
  scope :not_on_stop, -> { where(on_stop: false) }

  after_create :create_default_conversation
  before_validation :assign_default_cot_payment_plan

  def charting_base_chart
    base_chart || BaseChart.create(patient: self)
  end

  def patient_id
    id
  end

  def default_practice
    current_practice || practices.first
  end

  def assign_default_cot_payment_plan
    return if active_cot_payment_plan.present? || new_record?

    self.active_cot_payment_plan = cot_payment_plans.first || practices.first&.cot_payment_plans&.first
  end

  def create_default_conversation
    # Skip conversation creation during registration
    return if practices.empty?

    Conversation.create(patient: self, name: full_name, practice_id: practices.first&.id)
  end

  def linked_conversation
    conversation || Conversation.create(patient: self, name: full_name, practice_id: practices.first&.id)
  end

  def full_name_with_title_and_email
    if email.present?
      "#{full_name_with_title} (#{email})"
    else
      full_name_with_title
    end
  end

  def full_name_with_title
    [title, first_name, last_name].join(' ')
  end

  def full_name_with_email
    if email.present?
      "#{full_name} (#{email})"
    else
      full_name
    end
  end

  def initials
    initials = ''
    initials += first_name[0].upcase if first_name.present?
    initials += last_name[0].upcase if last_name.present?
    initials
  end

  def age
    date_of_birth ? ((Time.zone.today - date_of_birth).to_i / 365.25).floor : nil
  end

  def full_name
    [first_name, last_name].join(' ')
  end

  def balance_status
    case balance
    when 0
      'Clear'
    when 1..Float::INFINITY
      'Outstanding'
    else
      'Overdue'
    end
  end

  def patient_status
    return 'on_stop' if on_stop?
    return 'archived' if archived?

    'active'
  end

  def status_display_name
    case patient_status
    when 'on_stop'
      'Account On Stop'
    when 'archived'
      'Archived'
    else
      'Active'
    end
  end

  def balance
    opening_balance + payments_succeeded_sum - invoices_amount_due_sum
  end

  def age_in_years_and_months
    return nil if date_of_birth.blank?

    today = Time.zone.today
    years = today.year - date_of_birth.year
    months = today.month - date_of_birth.month

    # Adjust years and months if needed
    months -= 1 if today.day < date_of_birth.day

    if months.negative?
      years -= 1
      months += 12
    end

    if years.zero?
      "#{months} #{'month'.pluralize(months)}"
    elsif months.zero?
      "#{years} #{'year'.pluralize(years)}"
    else
      "#{years} #{'year'.pluralize(years)} #{months} #{'month'.pluralize(months)}"
    end
  end

  def self.ransackable_scopes(_auth_object = nil)
    super + [:fuzzy_search]
  end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[first_name last_name full_name date_of_birth mobile_phone email] + _ransackers.keys
  end

  ransacker :full_name do |parent|
    Arel::Nodes::InfixOperation.new('||',
                                    parent.table[:first_name], parent.table[:last_name])
  end

  attr_writer :login

  def login
    @login || email || mobile_phone
  end

  def self.find_for_database_authentication(warden_conditions)
    conditions = warden_conditions.dup
    if (login = conditions.delete(:login))
      formatted_number = if login.match?(/[^0-9 +]/)
                           login
                         else
                           (login.start_with?('+') ? login : "+44#{login.delete_prefix('0')}")
                         end
      where(conditions.to_h).where(['lower(email) = :value OR lower(mobile_phone) = :formatted_number',
                                    { :value => login.downcase, :formatted_number => formatted_number }]).first
    elsif conditions.key?(:mobile_phone) || conditions.key?(:email)
      where(conditions.to_h).first
    end
  end

  def requires_2fa?
    practices.count.positive? ? practices.first.two_factor_enabled : true
  end

  def self.send_reset_password_instructions(attributes = {})
    login = attributes[:login]
    patient = new

    if login.blank?
      patient.errors.add(:login, :blank)
      return patient
    end

    formatted_number =
      if login.match?(/[^0-9 +]/)
        login.downcase
      else
        login.start_with?('+') ? login : "+44#{login.delete_prefix('0')}"
      end

    resource = where(
      'lower(email) = :value OR lower(mobile_phone) = :value',
      value: formatted_number.downcase
    ).first

    if resource.present?
      resource.send_reset_password_instructions
      resource
    else
      patient.errors.add(:login, :not_found)
      patient
    end
  end

  def stripe_customer_id(practice)
    practices_patients.find_by(practice_id: practice.id)&.stripe_customer_id
  end

  def update_stripe_customer_id(practice, customer_id)
    practices_patient = practices_patients.find_by(practice_id: practice.id)

    practices_patient ||= practices_patients.create(practice: practice)

    practices_patient.update(stripe_customer_id: customer_id)
  end

  def full_address
    [address_line_1, address_line_2, city, postcode].compact.reject(&:blank?).join(', ')
  end

  def next_appointment_date
    closest_booking = calendar_bookings
                      .not_cancelled
                      .where('start_time > ?', Time.current)
                      .order(:start_time)
                      .first

    return '' if closest_booking.blank?

    closest_booking.start_time.in_time_zone('London').strftime('%A, %d %B %Y at %I:%M %p')
  end

  def as_json(options = {})
    super(options).merge({ 'next_appointment_date' => next_appointment_date })
  end

  def icon_state_related_records
    [
      calendar_bookings.not_cancelled,
      course_of_treatments.flat_map(&:charting_appointments)
    ].flatten.compact
  end

  private

  def payments_succeeded_sum
    if payments.loaded?
      payments.select { |p| p.status == 'succeeded' && p.invoice_id.nil? }.sum(&:amount)
    else
      payments.where(status: 'succeeded', invoice_id: nil).sum(:amount)
    end
  end

  def invoices_amount_due_sum
    if invoices.loaded?
      invoices.sum(&:amount_due)
    else
      invoices.includes(:invoice_items, payments: [:refunds]).sum(&:amount_due)
    end
  end

  protected

  def password_required?
    new_record? || password.present? || password_confirmation.present?
  end

  def email_required?
    false
  end
end
